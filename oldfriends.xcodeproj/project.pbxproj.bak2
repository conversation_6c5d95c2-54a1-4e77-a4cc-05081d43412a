// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		731BE22D2D720873002EF442 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 731BE2142D720870002EF442 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 731BE21B2D720870002EF442;
			remoteInfo = oldfriends;
		};
		731BE2372D720874002EF442 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 731BE2142D720870002EF442 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 731BE21B2D720870002EF442;
			remoteInfo = oldfriends;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		731BE21C2D720870002EF442 /* oldfriends.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = oldfriends.app; sourceTree = BUILT_PRODUCTS_DIR; };
		731BE22C2D720873002EF442 /* oldfriendsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = oldfriendsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		731BE2362D720874002EF442 /* oldfriendsUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = oldfriendsUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		731BE21E2D720870002EF442 /* oldfriends */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = oldfriends;
			sourceTree = "<group>";
		};
		731BE22F2D720873002EF442 /* oldfriendsTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = oldfriendsTests;
			sourceTree = "<group>";
		};
		731BE2392D720874002EF442 /* oldfriendsUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = oldfriendsUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		731BE2192D720870002EF442 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE2292D720873002EF442 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE2332D720874002EF442 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		731BE2132D720870002EF442 = {
			isa = PBXGroup;
			children = (
				731BE21E2D720870002EF442 /* oldfriends */,
				731BE22F2D720873002EF442 /* oldfriendsTests */,
				731BE2392D720874002EF442 /* oldfriendsUITests */,
				731BE21D2D720870002EF442 /* Products */,
			);
			sourceTree = "<group>";
		};
		731BE21D2D720870002EF442 /* Products */ = {
			isa = PBXGroup;
			children = (
				731BE21C2D720870002EF442 /* oldfriends.app */,
				731BE22C2D720873002EF442 /* oldfriendsTests.xctest */,
				731BE2362D720874002EF442 /* oldfriendsUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		731BE21B2D720870002EF442 /* oldfriends */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 731BE2402D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriends" */;
			buildPhases = (
				731BE2182D720870002EF442 /* Sources */,
				731BE2192D720870002EF442 /* Frameworks */,
				731BE21A2D720870002EF442 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				731BE21E2D720870002EF442 /* oldfriends */,
			);
			name = oldfriends;
			packageProductDependencies = (
			);
			productName = oldfriends;
			productReference = 731BE21C2D720870002EF442 /* oldfriends.app */;
			productType = "com.apple.product-type.application";
		};
		731BE22B2D720873002EF442 /* oldfriendsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 731BE2432D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriendsTests" */;
			buildPhases = (
				731BE2282D720873002EF442 /* Sources */,
				731BE2292D720873002EF442 /* Frameworks */,
				731BE22A2D720873002EF442 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				731BE22E2D720873002EF442 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				731BE22F2D720873002EF442 /* oldfriendsTests */,
			);
			name = oldfriendsTests;
			packageProductDependencies = (
			);
			productName = oldfriendsTests;
			productReference = 731BE22C2D720873002EF442 /* oldfriendsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		731BE2352D720874002EF442 /* oldfriendsUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 731BE2462D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriendsUITests" */;
			buildPhases = (
				731BE2322D720874002EF442 /* Sources */,
				731BE2332D720874002EF442 /* Frameworks */,
				731BE2342D720874002EF442 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				731BE2382D720874002EF442 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				731BE2392D720874002EF442 /* oldfriendsUITests */,
			);
			name = oldfriendsUITests;
			packageProductDependencies = (
			);
			productName = oldfriendsUITests;
			productReference = 731BE2362D720874002EF442 /* oldfriendsUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		731BE2142D720870002EF442 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					731BE21B2D720870002EF442 = {
						CreatedOnToolsVersion = 16.1;
					};
					731BE22B2D720873002EF442 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 731BE21B2D720870002EF442;
					};
					731BE2352D720874002EF442 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 731BE21B2D720870002EF442;
					};
				};
			};
			buildConfigurationList = 731BE2172D720870002EF442 /* Build configuration list for PBXProject "oldfriends" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 731BE2132D720870002EF442;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 731BE21D2D720870002EF442 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				731BE21B2D720870002EF442 /* oldfriends */,
				731BE22B2D720873002EF442 /* oldfriendsTests */,
				731BE2352D720874002EF442 /* oldfriendsUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		731BE21A2D720870002EF442 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE22A2D720873002EF442 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE2342D720874002EF442 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		731BE2182D720870002EF442 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE2282D720873002EF442 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		731BE2322D720874002EF442 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		731BE22E2D720873002EF442 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 731BE21B2D720870002EF442 /* oldfriends */;
			targetProxy = 731BE22D2D720873002EF442 /* PBXContainerItemProxy */;
		};
		731BE2382D720874002EF442 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 731BE21B2D720870002EF442 /* oldfriends */;
			targetProxy = 731BE2372D720874002EF442 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		731BE23E2D720874002EF442 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = oldfriends/Info.plist;
				"INFOPLIST_FILE[sdk=*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		731BE23F2D720874002EF442 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = oldfriends/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		731BE2412D720874002EF442 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"oldfriends/Preview Content\"";
				DEVELOPMENT_TEAM = 92N8C7L95T;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriends;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		731BE2422D720874002EF442 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"oldfriends/Preview Content\"";
				DEVELOPMENT_TEAM = 92N8C7L95T;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriends;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		731BE2442D720874002EF442 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 92N8C7L95T;
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriendsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/oldfriends.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/oldfriends";
			};
			name = Debug;
		};
		731BE2452D720874002EF442 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 92N8C7L95T;
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriendsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/oldfriends.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/oldfriends";
			};
			name = Release;
		};
		731BE2472D720874002EF442 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 92N8C7L95T;
				GENERATE_INFOPLIST_FILE = NO;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriendsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = oldfriends;
			};
			name = Debug;
		};
		731BE2482D720874002EF442 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 92N8C7L95T;
				GENERATE_INFOPLIST_FILE = NO;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.englishstudy.oldfriendsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = oldfriends;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		731BE2172D720870002EF442 /* Build configuration list for PBXProject "oldfriends" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				731BE23E2D720874002EF442 /* Debug */,
				731BE23F2D720874002EF442 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		731BE2402D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriends" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				731BE2412D720874002EF442 /* Debug */,
				731BE2422D720874002EF442 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		731BE2432D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriendsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				731BE2442D720874002EF442 /* Debug */,
				731BE2452D720874002EF442 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		731BE2462D720874002EF442 /* Build configuration list for PBXNativeTarget "oldfriendsUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				731BE2472D720874002EF442 /* Debug */,
				731BE2482D720874002EF442 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 731BE2142D720870002EF442 /* Project object */;
}
