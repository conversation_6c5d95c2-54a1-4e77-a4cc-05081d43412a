import Foundation

class SubtitleParser {
    // 解析字幕文件的主方法
    static func parseSubtitles(from content: String) -> [Subtitle] {
        var subtitles: [Subtitle] = []
        let lines = content.components(separatedBy: .newlines)
        var currentBlock: [String] = []
        var isInBlock = false
        
        // print("[字幕解析] 开始解析字幕文件，总行数: \(lines.count)")
        
        // 从第0行开始处理
        var i = 0
        while i < lines.count {
            let line = lines[i].trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 如果遇到序号，开始新的字幕块
            if isSubtitleIndex(line) {
                // 如果已经在处理一个块，先处理完当前块
                if !currentBlock.isEmpty {
                    if let subtitle = processSubtitleBlock(currentBlock) {
                        subtitles.append(subtitle)
                    } else {
                        // print("[字幕解析] 警告: 无法处理字幕块:")
                       // currentBlock.forEach {  print("  \($0)") }
                    }
                    currentBlock = []
                }
                
                isInBlock = true
                currentBlock = [line]
            }
            // 如果在字幕块中，添加行到当前块
            else if isInBlock {
                // 检查下一行是否是新的序号行
                let isLastLine = i == lines.count - 1
                let nextLine = !isLastLine ? lines[i + 1].trimmingCharacters(in: .whitespacesAndNewlines) : ""
                let nextLineIsIndex = !isLastLine && isSubtitleIndex(nextLine)
                
                // 添加当前行到块中（如果不是空行）
                if !line.isEmpty {
                    currentBlock.append(line)
                }
                
                // 如果下一行是新的序号行或者是文件结尾，处理当前块
                if nextLineIsIndex || isLastLine {
                    if !currentBlock.isEmpty {
                        if let subtitle = processSubtitleBlock(currentBlock) {
                            subtitles.append(subtitle)
                        } else {
                            // print("[字幕解析] 警告: 无法处理字幕块:")
                           // currentBlock.forEach {  print("  \($0)") }
                        }
                        currentBlock = []
                        isInBlock = false
                    }
                }
            }
            
            i += 1
        }
        
        validateParsingResult(subtitles)

        // 添加调试信息
        print("[字幕解析] 解析完成，总字幕数: \(subtitles.count)")
        if subtitles.count > 0 {
            print("[字幕解析] 第一个字幕时间: \(subtitles[0].startTime) - \(subtitles[0].endTime)")
            print("[字幕解析] 第一个字幕内容: \(subtitles[0].english) | \(subtitles[0].chinese)")
        }

        return subtitles
    }
    
    // 辅助方法：检查是否是字幕序号
    private static func isSubtitleIndex(_ line: String) -> Bool {
        let isNumber = line.rangeOfCharacter(from: CharacterSet.decimalDigits.inverted) == nil
        let isNotEmpty = !line.isEmpty
        return isNumber && isNotEmpty
    }
    
    // 辅助方法：处理字幕块
    private static func processSubtitleBlock(_ block: [String]) -> Subtitle? {
        // 1. 基本验证：至少需要3行（序号、时间轴、文本）
        guard block.count >= 3 else {
            // print("[字幕块处理] 错误: 行数不足")
            return nil
        }
        
        // 2. 验证序号行
        let index = block[0]
        guard isSubtitleIndex(index) else {
            // print("[字幕块处理] 错误: 无效的序号行: \(index)")
            return nil
        }
        
        // 3. 验证并解析时间行
        let timeLine = block[1]
        guard timeLine.contains("-->") else {
            // print("[字幕块处理] 错误: 无效的时间格式: \(timeLine)")
            return nil
        }
        
        let timeComponents = timeLine.components(separatedBy: "-->").map { $0.trimmingCharacters(in: .whitespaces) }
        guard timeComponents.count == 2,
              let startTime = parseTime(timeComponents[0]),
              let endTime = parseTime(timeComponents[1]) else {
            // print("[字幕块处理] 错误: 时间解析失败: \(timeLine)")
            return nil
        }
        
        // 4. 处理文本行
        let textLines = Array(block[2...])
        let (english, chinese) = processTextLines(textLines)
        
        // 5. 特殊处理序号为0的字幕
        if index == "0" {
            return Subtitle(
                index: index,
                startTime: startTime,
                endTime: endTime,
                english: "",
                chinese: "多听一次，多进步一点点！"
            )
        }
        
        // 6. 确保至少有一种语言的文本
        guard !chinese.isEmpty || !english.isEmpty else {
            // print("[字幕块处理] 错误: 无有效文本内容")
            return nil
        }
        
        return Subtitle(
            index: index,
            startTime: startTime,
            endTime: endTime,
            english: english,
            chinese: chinese
        )
    }
    
    // 辅助方法：处理文本行
    private static func processTextLines(_ lines: [String]) -> (english: String, chinese: String) {
        var english = ""
        var chinese = ""
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmedLine.isEmpty { continue }
            
            if containsChineseCharacters(trimmedLine) {
                if chinese.isEmpty {
                    chinese = trimmedLine
                } else {
                    chinese += " " + trimmedLine
                }
            } else if containsEnglishLetters(trimmedLine) {
                if english.isEmpty {
                    english = trimmedLine
                } else {
                    english += " " + trimmedLine
                }
            }
        }
        
        return (english, chinese)
    }
    
    // 辅助方法：解析时间
    private static func parseTime(_ timeString: String) -> TimeInterval? {
        // 支持两种时间格式：
        // 1. 标准格式：00:00:00,123
        // 2. 特殊格式：00:03:356 (这里是 00分钟:03秒:356毫秒)

        if timeString.contains(",") {
            // 标准格式：00:00:00,123
            let components = timeString.components(separatedBy: CharacterSet(charactersIn: ":,"))
            guard components.count == 4,
                  let hours = Int(components[0]),
                  let minutes = Int(components[1]),
                  let seconds = Int(components[2]),
                  let milliseconds = Int(components[3]) else {
                print("[时间解析] 标准格式解析失败: \(timeString)")
                return nil
            }
            let result = TimeInterval(hours * 3600 + minutes * 60 + seconds) + TimeInterval(milliseconds) / 1000.0
            print("[时间解析] 标准格式: \(timeString) -> \(result)秒")
            return result
        } else {
            // 特殊格式：00:03:356 (00分钟:03秒:356毫秒)
            let components = timeString.components(separatedBy: ":")
            guard components.count == 3,
                  let minutes = Int(components[0]),
                  let seconds = Int(components[1]),
                  let milliseconds = Int(components[2]) else {
                print("[时间解析] 特殊格式解析失败: \(timeString)")
                return nil
            }

            // 按照 分钟:秒:毫秒 格式解析
            let result = TimeInterval(minutes * 60 + seconds) + TimeInterval(milliseconds) / 1000.0
//            print("[时间解析] 特殊格式: \(timeString) -> \(result)秒 (解析: \(minutes)分钟\(seconds)秒\(milliseconds)毫秒)")
            return result
        }
    }
    
    // 辅助方法：检查是否包含英文字母
    private static func containsEnglishLetters(_ str: String) -> Bool {
        let pattern = "[A-Za-z]"
        return str.range(of: pattern, options: .regularExpression) != nil
    }
    
    // 辅助方法：检查是否包含中文字符
    private static func containsChineseCharacters(_ str: String) -> Bool {
        let pattern = "\\p{Han}"
        return str.range(of: pattern, options: .regularExpression) != nil
    }
    
    // 辅助方法：验证解析结果
    private static func validateParsingResult(_ subtitles: [Subtitle]) {
        // print("[字幕解析] 解析完成，总字幕数: \(subtitles.count)")
        
        if subtitles.isEmpty {
            // print("[字幕解析] 错误: 未能解析出任何字幕")
        } else if subtitles.count < 10 {
            // print("[字幕解析] 警告: 解析出的字幕数量异常少 (\(subtitles.count))")
        }
    }
} 
