import UIKit
@preconcurrency import WebKit


enum PDFGenerator {
    private actor CompletionState {
        private var isCompleted = false
        
        func complete() -> <PERSON>ol {
            if !isCompleted {
                isCompleted = true
                return true
            }
            return false
        }
        
        func isAlreadyCompleted() -> <PERSON>ol {
            return isCompleted
        }
    }
    
    static func generateWordsPDF(words: [(word: String, episode: (seasonNumber: Int, episodeNumber: Int)?)], theme: AppTheme) async -> URL? {
        // print("[PDFGenerator] 开始生成单词PDF，单词数量: \(words.count)")
        
        let htmlContent = generateWordsHTML(words: words)
        // print("[PDFGenerator] HTML内容长度: \(htmlContent.count)")
        
        let completionState = CompletionState()
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                // 创建配置
                let config = WKWebViewConfiguration()
                let preferences = WKWebpagePreferences()
                preferences.allowsContentJavaScript = true
                config.defaultWebpagePreferences = preferences
                
                // 创建 WebView
                let webView = WKWebView(frame: .init(x: 0, y: 0, width: 595.2, height: 841.8), 
                                      configuration: config)
                
                let converter = HTMLToPDFConverter()
                webView.navigationDelegate = converter
                
                // 设置进度观察者
                converter.startObservingProgress(for: webView)
                
                // print("[PDFGenerator] 开始加载HTML内容")
                
                converter.progressHandler = { progress in
                    // print("[PDFGenerator] 加载进度: \(progress)")
                }
                
                converter.completionHandler = { [weak webView] in
                    // print("[PDFGenerator] WebView加载完成")
                    guard let webView = webView else {
                        Task {
                            if await completionState.complete() {
                                // print("[PDFGenerator] WebView已被释放")
                                continuation.resume(returning: nil)
                            }
                        }
                        return
                    }
                    
                    // 等待一小段时间确保内容完全渲染
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // print("[PDFGenerator] 开始生成PDF文件")
                        generatePDF(from: webView, fileName: "老友记学英语-Words") { url in
                            // 移除观察者
                            converter.stopObservingProgress(for: webView)
                            
                            Task {
                                if await completionState.complete() {
                                    if let url = url {
                                        // print("[PDFGenerator] PDF生成成功")
                                        continuation.resume(returning: url)
                                    } else {
                                        // print("[PDFGenerator] PDF生成失败")
                                        continuation.resume(returning: nil)
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 延长超时时间
                DispatchQueue.main.asyncAfter(deadline: .now() + 60) {
                    Task {
                        if await completionState.complete() {
                            // 移除观察者
                            converter.stopObservingProgress(for: webView)
                            // print("[PDFGenerator] 生成PDF超时（60秒）")
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                converter.loadErrorHandler = { error in
                    // print("[PDFGenerator] HTML加载失败: \(error.localizedDescription)")
                    // 移除观察者
                    converter.stopObservingProgress(for: webView)
                    Task {
                        if await completionState.complete() {
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                // 使用 baseURL 来确保资源加载正确
                let baseURL = Bundle.main.bundleURL
                webView.loadHTMLString(htmlContent, baseURL: baseURL)
            }
        }
    }
    
    static func generateSentencesPDF(sentences: [FavoriteSentence], theme: AppTheme) async -> URL? {
        // print("[PDFGenerator] 开始生成句子PDF，句子数量: \(sentences.count)")
        
        let htmlContent = generateSentencesHTML(sentences: sentences)
        // print("[PDFGenerator] HTML内容长度: \(htmlContent.count)")
        
        let completionState = CompletionState()
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                // 创建配置
                let config = WKWebViewConfiguration()
                let preferences = WKWebpagePreferences()
                preferences.allowsContentJavaScript = true
                config.defaultWebpagePreferences = preferences
                
                // 创建 WebView
                let webView = WKWebView(frame: .init(x: 0, y: 0, width: 595.2, height: 841.8),
                                      configuration: config)
                let converter = HTMLToPDFConverter()
                webView.navigationDelegate = converter
                
                // 设置进度观察者
                converter.startObservingProgress(for: webView)
                
                // print("[PDFGenerator] 开始加载HTML内容")
                
                converter.progressHandler = { progress in
                    // print("[PDFGenerator] 加载进度: \(progress)")
                }
                
                converter.completionHandler = { [weak webView] in
                    // print("[PDFGenerator] WebView加载完成")
                    guard let webView = webView else {
                        Task {
                            if await completionState.complete() {
                                // print("[PDFGenerator] WebView已被释放")
                                continuation.resume(returning: nil)
                            }
                        }
                        return
                    }
                    
                    // 等待一小段时间确保内容完全渲染
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // print("[PDFGenerator] 开始生成PDF文件")
                        generatePDF(from: webView, fileName: "听商务英语-Sentences") { url in
                            // 移除观察者
                            converter.stopObservingProgress(for: webView)
                            
                            Task {
                                if await completionState.complete() {
                                    if let url = url {
                                        // print("[PDFGenerator] PDF生成成功")
                                        continuation.resume(returning: url)
                                    } else {
                                        // print("[PDFGenerator] PDF生成失败")
                                        continuation.resume(returning: nil)
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 时时间到60秒
                DispatchQueue.main.asyncAfter(deadline: .now() + 60) {
                    Task {
                        if await completionState.complete() {
                            // 移除观察者
                            converter.stopObservingProgress(for: webView)
                            // print("[PDFGenerator] 生成PDF超时（60秒）")
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                converter.loadErrorHandler = { error in
                    // print("[PDFGenerator] HTML加载失败: \(error.localizedDescription)")
                    // 移除观察者
                    converter.stopObservingProgress(for: webView)
                    Task {
                        if await completionState.complete() {
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                // 使用 baseURL 来确保资源加载正确
                let baseURL = Bundle.main.bundleURL
                webView.loadHTMLString(htmlContent, baseURL: baseURL)
            }
        }
    }
    
    // 添加生成字幕PDF的方法
    static func generateSubtitlesPDF(subtitles: [Subtitle], seasonNumber: Int, episodeNumber: Int, theme: AppTheme) async -> URL? {
        // print("[PDFGenerator] 开始生成字幕PDF，字幕数量: \(subtitles.count)")
        
        let htmlContent = generateSubtitlesHTML(subtitles: subtitles, seasonNumber: seasonNumber, episodeNumber: episodeNumber)
        // print("[PDFGenerator] HTML内容长度: \(htmlContent.count)")
        
        let completionState = CompletionState()
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                // 创建配置
                let config = WKWebViewConfiguration()
                let preferences = WKWebpagePreferences()
                preferences.allowsContentJavaScript = true
                config.defaultWebpagePreferences = preferences
                
                // 创建 WebView
                let webView = WKWebView(frame: .init(x: 0, y: 0, width: 595.2, height: 841.8),
                                      configuration: config)
                let converter = HTMLToPDFConverter()
                webView.navigationDelegate = converter
                
                // 设置进度观察者
                converter.startObservingProgress(for: webView)
                
                // print("[PDFGenerator] 开始加载HTML内容")
                
                converter.progressHandler = { progress in
                    // print("[PDFGenerator] 加载进度: \(progress)")
                }
                
                converter.completionHandler = { [weak webView] in
                    // print("[PDFGenerator] WebView加载完成")
                    guard let webView = webView else {
                        Task {
                            if await completionState.complete() {
                                // print("[PDFGenerator] WebView已被释放")
                                continuation.resume(returning: nil)
                            }
                        }
                        return
                    }
                    
                    // 等待一小段时间确保内容完全渲染
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // print("[PDFGenerator] 开始生成PDF文件")
                        generatePDF(from: webView, fileName: "老友记学英语-S\(seasonNumber)E\(episodeNumber)") { url in
                            // 移除观察者
                            converter.stopObservingProgress(for: webView)
                            
                            Task {
                                if await completionState.complete() {
                                    if let url = url {
                                        // print("[PDFGenerator] PDF生成成功")
                                        continuation.resume(returning: url)
                                    } else {
                                        // print("[PDFGenerator] PDF生成失败")
                                        continuation.resume(returning: nil)
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 设置超时
                DispatchQueue.main.asyncAfter(deadline: .now() + 60) {
                    Task {
                        if await completionState.complete() {
                            converter.stopObservingProgress(for: webView)
                            // print("[PDFGenerator] 生成PDF超时（60秒）")
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                converter.loadErrorHandler = { error in
                    // print("[PDFGenerator] HTML加载失败: \(error.localizedDescription)")
                    converter.stopObservingProgress(for: webView)
                    Task {
                        if await completionState.complete() {
                            continuation.resume(returning: nil)
                        }
                    }
                }
                
                // 使用 baseURL 来确保资源加载正确
                let baseURL = Bundle.main.bundleURL
                webView.loadHTMLString(htmlContent, baseURL: baseURL)
            }
        }
    }
    
    // 生成单词的 HTML 内容
    private static func generateWordsHTML(words: [(word: String, episode: (seasonNumber: Int, episodeNumber: Int)?)]) -> String {
        var html = baseHTMLTemplate(title: "我的生词本")
        
        for wordInfo in words {
            if let entry = DictionaryDatabase.shared.lookup(wordInfo.word) {
                let translation = entry.translation ?? ""
                
                if let dotIndex = translation.firstIndex(of: ".") {
                    let partOfSpeech = String(translation[..<dotIndex]) + "."
                    
                    let meaningStart = translation.index(after: dotIndex)
                    let meaningText = String(translation[meaningStart...])
                        .trimmingCharacters(in: CharacterSet.whitespaces)
                    
                    // 修改这里：简化处理方式，直接处理每个词性的内容
                    let meanings = meaningText
                        .components(separatedBy: "\\n")
                        .map { $0.trimmingCharacters(in: .whitespaces) }
                        .filter { !$0.isEmpty }
                        .joined(separator: "<br>")
                    
                    let episodeInfo = wordInfo.episode.map { episode in
                        "S\(episode.seasonNumber)E\(episode.episodeNumber)"
                    } ?? ""
                    
                    // 修改 HTML 结构，使用表格式布局
                    html += """
                        <div class="word-item">
                            <div class="word-header">
                                <div class="word-main">
                                    <span class="word">\(wordInfo.word)</span>
                                    <span class="phonetic">[\(entry.phonetic ?? "")]</span>
                                </div>
                                <div class="episode">\(episodeInfo)</div>
                            </div>
                            <div class="word-content">
                                <table class="translation-table">
                                    <tr>
                                        <td class="part-of-speech">\(partOfSpeech)</td>
                                        <td class="meanings">\(meanings)</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    """
                }
            }
        }
        
        return html + "</body></html>"
    }
    
    // 生成句子的 HTML 内容
    private static func generateSentencesHTML(sentences: [FavoriteSentence]) -> String {
        var html = baseHTMLTemplate(title: "我的句子本")
        
        for sentence in sentences {
            html += """
                <div class="sentence-item">
                    <div class="sentence-header">
                        <div class="sentence-content">
                            <div class="english">\(sentence.english)</div>
                            <div class="chinese">\(sentence.chinese)</div>
                        </div>
                        <div class="episode">S\(sentence.seasonNumber)E\(sentence.episodeNumber)</div>
                    </div>
                </div>
            """
        }
        
        return html + "</body></html>"
    }
    
    // 生成字幕的 HTML 内容
    private static func generateSubtitlesHTML(subtitles: [Subtitle], seasonNumber: Int, episodeNumber: Int) -> String {
        let title = "S\(seasonNumber)E\(episodeNumber) 字幕"
        var html = baseHTMLTemplate(title: title)
        
        for subtitle in subtitles {
            html += """
                <div class="sentence-item">
                    <div class="sentence-header">
                        <div class="sentence-content">
                            <div class="english">\(subtitle.english)</div>
                            <div class="chinese">\(subtitle.chinese)</div>
                        </div>
                        <div class="time">\(formatTime(subtitle.startTime)) - \(formatTime(subtitle.endTime))</div>
                    </div>
                </div>
            """
        }
        
        return html + "</body></html>"
    }
    
    // 格式化时间
    private static func formatTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) / 60 % 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    // 基础 HTML 模板
    private static func baseHTMLTemplate(title: String) -> String {
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {
                    margin-top: 40px;
                }
                
                body { 
                    font-family: -apple-system; 
                    padding: 40px 40px;
                    margin: 0;
                    line-height: 1.5;
                }
                
                .title { 
                    font-size: 24px; 
                    text-align: center;
                    margin-bottom: 40px;
                }
                
                .sentence-item {
                    padding: 20px 0;
                    border-bottom: 1px solid #EEEEEE;
                    position: relative;
                    page-break-inside: avoid;
                    break-inside: avoid;
                   
                }
                
                .sentence-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                   
                }
                
                .sentence-content {
                    flex: 1;
                    margin-right: 12px;
                 
                }
                
                .english {
                    font-size: 18px;
                    font-weight: 600;
                    color: #000;
                    margin-bottom: 10px;
                    line-height: 1.5;
                }
                
                .chinese {
                    font-size: 14px;
                    color: #333;
                    line-height: 1.5;
                }
                
                .episode {
                    font-size: 12px;
                    color: #666666;
                   
                    font-weight: 400;
                    white-space: nowrap;
                }
                
                .word-item {
                    padding: 16px 0;
                    border-bottom: 1px solid #EEEEEE;
                    position: relative;
                    page-break-inside: avoid;
                    break-inside: avoid;
                }
                
                .word-header {
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                }
                
                .word-main {
                    display: flex;
                    align-items: baseline;
                    flex: 1;
                    margin-right: 12px;
                }
                
                .word {
                    font-size: 20px;
                    font-weight: 500;
                    color: #000000;
                    margin-right: 8px;
                }
                
                .phonetic {
                    font-size: 16px;
                    color: #666666;
                }
                
                .episode {
                    font-size: 12px;
                    color: #666666;
                    padding: 2px 8px;
                    
                    font-weight: 400;
                    white-space: nowrap;
                }
                
                .word-content {
                    margin-top: 8px;
                }
                
                .translation-container {
                    display: flex;
                    align-items: flex-start;
                }
                
                .part-of-speech {
                    font-size: 14px;
                    color: #666666;
                    white-space: nowrap;
                    margin-right: 0;
                }
                
                .meanings {
                    flex: 1;
                    font-size: 14px;
                    color: #333333;
                    line-height: 1.8;
                    margin-left: 0;
                    padding-left: 0;
                    text-align: left;
                }
                
                .meaning-line {
                    text-indent: 0;
                    padding-left: 0;
                    margin-left: 0;
                    display: block;
                }
                
                .translation-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 8px;
                }
                
                .part-of-speech {
                    font-size: 14px;
                    color: #666666;
                    white-space: nowrap;
                    vertical-align: top;
                    padding-right: 8px;
                    width: 1%;
                }
                
                .meanings {
                    font-size: 14px;
                    color: #333333;
                    line-height: 1.8;
                    vertical-align: top;
                    text-align: left;
                }
                
                /* 确保换行后的文本完全左对齐 */
                .meanings br + * {
                    text-indent: 0;
                    padding-left: 0;
                    margin-left: 0;
                }
            </style>
        </head>
        <body>
            <div class="title">\(title)</div>
        """
    }
    
    // 生成 PDF 文件
    private static func generatePDF(from webView: WKWebView, fileName: String, completion: @escaping (URL?) -> Void) {
        let printFormatter = webView.viewPrintFormatter()
        let renderer = UIPrintPageRenderer()
        renderer.addPrintFormatter(printFormatter, startingAtPageAt: 0)
        
        let pageSize = CGRect(x: 0, y: 0, width: 595.2, height: 841.8)
        renderer.setValue(pageSize, forKey: "paperRect")
        renderer.setValue(pageSize, forKey: "printableRect")
        
        let pdfData = NSMutableData()
        UIGraphicsBeginPDFContextToData(pdfData, pageSize, nil)
        
        for i in 0..<renderer.numberOfPages {
            UIGraphicsBeginPDFPage()
            renderer.drawPage(at: i, in: UIGraphicsGetPDFContextBounds())
        }
        
        UIGraphicsEndPDFContext()
        
        // 先保存到临时目录
        let tempDir = FileManager.default.temporaryDirectory
        let pdfUrl = tempDir.appendingPathComponent("\(fileName).pdf")
        
        do {
            try pdfData.write(to: pdfUrl)
            // print("[PDFGenerator] PDF文件成功生成到临时目录: \(pdfUrl)")
            
            // 在主线程显示文件选择器
            DispatchQueue.main.async {
                let documentPicker = UIDocumentPickerViewController(forExporting: [pdfUrl])
                documentPicker.shouldShowFileExtensions = true
                
                // 获取当前最顶层的视图控制器
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootViewController = window.rootViewController {
                    var topViewController = rootViewController
                    while let presentedViewController = topViewController.presentedViewController {
                        topViewController = presentedViewController
                    }
                    
                    // 显示文件选择器
                    topViewController.present(documentPicker, animated: true) {
                        // 文件选择器显示后，返回临时文件URL
                        completion(pdfUrl)
                    }
                } else {
                    // print("[PDFGenerator] 无法获取顶层视图控制器")
                    completion(pdfUrl)
                }
            }
        } catch {
            // print("[PDFGenerator] PDF文件保存失败: \(error)")
            completion(nil)
        }
    }
}

// HTML to PDF 转换器
private class HTMLToPDFConverter: NSObject, WKNavigationDelegate {
    var completionHandler: (() -> Void)?
    var loadErrorHandler: ((Error) -> Void)?
    var progressHandler: ((Double) -> Void)?
    private var observing = false
    
    func startObservingProgress(for webView: WKWebView) {
        if !observing {
            webView.addObserver(self, 
                              forKeyPath: #keyPath(WKWebView.estimatedProgress), 
                              options: [.new], 
                              context: nil)
            observing = true
        }
    }
    
    func stopObservingProgress(for webView: WKWebView) {
        if observing {
            webView.removeObserver(self, 
                                 forKeyPath: #keyPath(WKWebView.estimatedProgress))
            observing = false
        }
    }
    
    override func observeValue(forKeyPath keyPath: String?,
                             of object: Any?,
                             change: [NSKeyValueChangeKey : Any]?,
                             context: UnsafeMutableRawPointer?) {
        if keyPath == #keyPath(WKWebView.estimatedProgress),
           let progress = change?[.newKey] as? Double {
            progressHandler?(progress)
        }
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // print("[PDFGenerator] WebView.didFinish 被调用")
        completionHandler?()
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        // print("[PDFGenerator] WebView.didFail 被调用: \(error.localizedDescription)")
        loadErrorHandler?(error)
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        // print("[PDFGenerator] WebView.didFailProvisionalNavigation 被调用: \(error.localizedDescription)")
        loadErrorHandler?(error)
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        // print("[PDFGenerator] WebView.decidePolicyFor 被调用")
        decisionHandler(.allow)
    }
    
    deinit {
        if observing {
            // print("[PDFGenerator] 警告: HTMLToPDFConverter 在 deinit 时仍在观察进度")
        }
    }
} 
