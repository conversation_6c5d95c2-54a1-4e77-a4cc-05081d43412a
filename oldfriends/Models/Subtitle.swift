import Foundation

public struct Subtitle: Identifiable {
    public let id = UUID()
    public let index: String
    public let startTime: TimeInterval
    public let endTime: TimeInterval
    public let english: String
    public let chinese: String
    
    public init(
        index: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        english: String,
        chinese: String
    ) {
        self.index = index
        self.startTime = startTime
        self.endTime = endTime
        self.english = english
        self.chinese = chinese
    }
} 