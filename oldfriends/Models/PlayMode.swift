import Foundation

public enum PlayMode: String, CaseIterable {
    case sequential = "sequential"      // 顺序播放
    case singleSentence = "sentence"    // 单句循环
    case singleEpisode = "episode"      // 单集循环
    case abLoop = "ab"                  // AB循环
    
    public var description: String {
        switch self {
        case .sequential:
            return "顺序播放"
        case .singleSentence:
            return "单句循环"
        case .singleEpisode:
            return "单集循环"
        case .abLoop:
            return "AB循环"
        }
    }

    public var icon: String {
        switch self {
        case .sequential:
            return "arrow.triangle.2.circlepath"    // 使用更合适的系统循环图标
        case .singleSentence:
            return "repeat.1.circle"                       // 使用圆圈中的数字1
        case .singleEpisode:
            return "repeat.circle"                  // 保持不变，已经是系统图标
        case .abLoop:
            return "arrow.left.and.right.circle"    // 使用圆圈版本的左右箭头
        }
    }
    
    public mutating func toggle() {
        let allModes = PlayMode.allCases
        let currentIndex = allModes.firstIndex(of: self)!
        let nextIndex = (currentIndex + 1) % allModes.count
        self = allModes[nextIndex]
    }
    
    // 修改这两个方法，使用公共的 Subtitle 类型
    public func isBeforeAPoint(subtitle: Subtitle, abLoopStartTime: TimeInterval?) -> Bool {
        guard case .abLoop = self,
              let startTime = abLoopStartTime else {
            return false
        }
        let result = subtitle.endTime < startTime
        return result
    }
    
    public func isAPoint(subtitle: Subtitle, abLoopStartTime: TimeInterval?) -> Bool {
        guard case .abLoop = self,
              let startTime = abLoopStartTime else {
            return false
        }
        let result = subtitle.startTime == startTime
        // print("[PlayMode] 检查字幕是否是A点 - 字幕开始时间: \(subtitle.startTime), A点时间: \(startTime), 结果: \(result)")
        return result
    }
} 