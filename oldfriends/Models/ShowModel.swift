import Foundation

// 剧集数据模型
struct Show: Identifiable, Codable {
    let title: String
    let seasons: [Season]
    
    var id: UUID {
        // 使用 title 作为唯一标识的来源
        UUID(uuidString: title) ?? UUID()
    }
    
    private enum CodingKeys: String, CodingKey {
        case title, seasons
    }
}

// 季数据模型
struct Season: Identifiable, Codable {
    let number: Int
    let episodes: [Episode]
    
    var id: UUID {
        // 使用 number 作为唯一标识的来源
        UUID(uuidString: String(number)) ?? UUID()
    }
    
    private enum CodingKeys: String, CodingKey {
        case number, episodes
    }
}

// 单集数据模型
struct Episode: Identifiable, Codable {
    let number: Int
    let title: String
    let audioName: String
    let subtitleName: String
    
    var id: UUID {
        // 使用 number 和 title 组合作为唯一标识的来源
        UUID(uuidString: "\(number)_\(title)") ?? UUID()
    }
    
    private enum CodingKeys: String, CodingKey {
        case number, title, audioName, subtitleName
    }
} 