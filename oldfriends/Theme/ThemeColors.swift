import SwiftUI
import Foundation

/*
 * 文件名：ThemeColors.swift
 * 功能：定义应用的颜色主题系统
 * 上游依赖：
 *   - SwiftUI：提供Color支持
 * 下游调用：
 *   - 所有视图文件：使用颜色主题
 */

// 添加 Color 扩展来支持十六进制颜色
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// 主题枚举
enum AppTheme {
    case dark
    case light
}

// 主题颜色配置
struct ThemeColors {
    // MARK: - 主色系统

    // 品牌主色
    static let primary = Color(hex: "#4F9CF9") // App Icon O字母蓝
    static let primaryHover = Color(hex: "#3B82F6") // 悬停状态
    static let primaryActive = Color(hex: "#2563EB") // 激活状态

    // 兼容旧版本的 accent 颜色，映射到 primary
    @available(*, deprecated, message: "请使用 primary 替代")
    static var accent: Color { primary }

    // 中性色
    static func background(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#1A202C") : Color(hex: "#F7FAFC") // 书页白/深色背景
    }

    static func surface(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#2D3748") : Color(hex: "#FFFFFF") // 纯白/深色表面
    }

    // 主文本颜色
    static func primaryText(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#F7FAFC") : Color(hex: "#1A202C") // 墨黑/亮色文本
    }

    // 次要文本颜色
    static func secondaryText(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#A0AEC0") : Color(hex: "#4A5568") // 石墨/次要文本
    }

    // 边框颜色
    static func border(_ theme: AppTheme) -> Color {
        theme == .dark ? Color.white.opacity(0.1) : Color(hex: "#E2E8F0") // 银灰/深色边框
    }

    // MARK: - 功能色系统

    // 状态色
    static let success = Color(hex: "#48BB78") // 学习绿
    static let warning = Color(hex: "#ECC94B") // 提醒黄
    static let error = Color(hex: "#F56565") // 错误红
    static let info = Color(hex: "#4299E1") // 提示蓝

    // 学习模块色
    static let videoModule = Color(hex: "#BEE3F8") // 影像模块
    static let wordModule = Color(hex: "#C6F6D5") // 单词模块
    static let speakModule = Color(hex: "#FEEBC8") // 跟读模块
    static let gameModule = Color(hex: "#FED7E2") // 游戏模块
    static let favoriteModule = Color(hex: "#E9D8FD") // 收藏模块

    // MARK: - 应用场景

    // 标题颜色
    static func titleColor(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#F7FAFC") : primary
    }

    // 字幕背景色
    static func subtitleBackground(_ theme: AppTheme, isHighlighted: Bool) -> Color {
        if theme == .dark {
            return Color.white.opacity(isHighlighted ? 0.12 : 0.06)
        } else {
            return Color.black.opacity(isHighlighted ? 0.08 : 0.03)
        }
    }

    // 控制栏背景色
    static func controlBarBackground(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#1A202C").opacity(0.9) : Color(hex: "#F7FAFC").opacity(0.9)
    }

    // 图标颜色
    static func iconColor(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#A0AEC0") : Color(hex: "#4A5568")
    }

    // 播放器背景
    static func playerBackground(_ theme: AppTheme) -> Color {
        theme == .dark ? Color(hex: "#1A202C") : Color(hex: "#F7FAFC")
    }

    // 进度条
    static let progressBar = primary

    // 字幕文字
    static let subtitleText = Color(hex: "#FFFFFF")

    // 波形图
    static let waveform = primary

    // 评分展示
    static let scoreDisplay = Color(hex: "#48BB78")

    // 录音按钮
    static let recordButton = Color(hex: "#F56565")
}