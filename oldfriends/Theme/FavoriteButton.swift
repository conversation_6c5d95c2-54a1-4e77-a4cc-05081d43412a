import SwiftUI

struct FavoriteButton: View {
    @Binding var isFavorited: Bool

    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
                isFavorited.toggle()
            }
        }) {
            Image(systemName: isFavorited ? "star.fill" : "star")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isFavorited ? ThemeColors.primary : ThemeColors.secondaryText(.light))
                .scaleEffect(isFavorited ? 1.1 : 1.0)
                .shadow(
                    color: isFavorited ? ThemeColors.primary.opacity(0.2) : .clear,
                    radius: isFavorited ? 3 : 0,
                    x: 0, y: 1
                )
                .accessibilityLabel(isFavorited ? "已收藏" : "收藏")
        }
        .buttonStyle(PlainButtonStyle())
    }
}