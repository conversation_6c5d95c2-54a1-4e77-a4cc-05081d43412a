import Foundation
import FMDB
import SQLite3

// 修改数据库名称
private let dbName = "ecdict"

struct DictionaryEntry: Codable {
    let word: String
    let translation: String?
    let phonetic: String?
    let definition: String?
    let exchange: String?
}

class DictionaryDatabase {
    static let shared = DictionaryDatabase()
    private var db: FMDatabase?
    private let tableName = "stardict"
    
    private init() {
        // 添加更多调试信息
        // print("Looking for database file: \(dbName).db")
        
        // 1. 先尝试在 Resources 目录下查找
        if let resourcePath = Bundle.main.resourcePath {
            let resourcesPath = (resourcePath as NSString).appendingPathComponent("Resources")
            let dbPath = (resourcesPath as NSString).appendingPathComponent("\(dbName).db")
            
            // print("Trying path: \(dbPath)")
            if FileManager.default.fileExists(atPath: dbPath) {
                db = FMDatabase(path: dbPath)
            }
        }
        
        // 2. 如果在 Resources 中找不到，尝试直接在 bundle 根目录查找
        if db == nil {
            if let path = Bundle.main.path(forResource: dbName, ofType: "db") {
                // print("Found database at path: \(path)")
                db = FMDatabase(path: path)
            }
        }
        
        // 3. 如果都找不到，打印错误
        if db == nil {
             print("Could not find \(dbName).db in bundle")
            if let resourcePath = Bundle.main.resourcePath {
                 print("Bundle resource path: \(resourcePath)")
                do {
                    let contents = try FileManager.default.contentsOfDirectory(atPath: resourcePath)
                     print("Files in bundle: \(contents)")
                } catch {
                     print("Error listing bundle contents: \(error)")
                }
            }
            return
        }
        
        guard let db = db else {
            // print("Cannot create database")
            return
        }
        
        if !db.open() {
            // print("Cannot open database: \(db.lastErrorMessage())")
            return
        }
        
        // 检查数据库结构
        // print("Checking database structure...")
        
        // 1. 检查表是否存在
        let tableCheck = "SELECT name FROM sqlite_master WHERE type='table' AND name='\(tableName)'"
        if let rs = db.executeQuery(tableCheck, withArgumentsIn: []) {
            if rs.next() {
                // print("Table '\(tableName)' exists")
                
                // 2. 检查表结构
                let columnsQuery = "PRAGMA table_info(\(tableName))"
                if let columnsRs = db.executeQuery(columnsQuery, withArgumentsIn: []) {
                    while columnsRs.next() {
                        _ = columnsRs.string(forColumn: "name") ?? ""
                        _ = columnsRs.string(forColumn: "type") ?? ""
                    }
                    columnsRs.close()
                }
                
                // 3. 检查记录数
                let countQuery = "SELECT COUNT(*) as count FROM \(tableName)"
                if let countRs = db.executeQuery(countQuery, withArgumentsIn: []) {
                    if countRs.next() {
//                        let count = countRs.int(forColumn: "count")
                        // print("Total records: \(count)")
                    }
                    countRs.close()
                }
            } else {
                 print("Table '\(tableName)' does not exist")
                // 打印所有表名
                let tablesQuery = "SELECT name FROM sqlite_master WHERE type='table'"
                if let tablesRs = db.executeQuery(tablesQuery, withArgumentsIn: []) {
                     print("Available tables:")
                    while tablesRs.next() {
                        if let tableName = tablesRs.string(forColumn: "name") {
                             print("- \(tableName)")
                        }
                    }
                    tablesRs.close()
                }
            }
            rs.close()
        } else {
            // print("Failed to check table: \(db.lastErrorMessage())")
        }
        
        // 设置数据库参数以提高稳定性
        db.executeStatements("PRAGMA journal_mode = WAL")
        db.executeStatements("PRAGMA synchronous = NORMAL")
        db.executeStatements("PRAGMA cache_size = 10000")
        db.executeStatements("PRAGMA temp_store = memory")
        db.executeStatements("PRAGMA busy_timeout = 30000")
    }
    
    deinit {
        db?.close()
    }
    
    func lookup(_ word: String) -> DictionaryEntry? {
        guard let db = db else {
            print("[DictionaryDB] 数据库连接失败")
            return nil
        }

        // 检查数据库连接状态
        if !db.isOpen {
            print("[DictionaryDB] 数据库连接状态异常，尝试重新连接")
            if !reopenDatabase() {
                print("[DictionaryDB] 重新连接失败")
                return nil
            }
        }

        let queryString = """
            SELECT word, translation, phonetic, definition, exchange
            FROM stardict
            WHERE word = ? COLLATE NOCASE
        """

        print("[DictionaryDB] 开始查询单词: \(word)")

        var result: DictionaryEntry?

        if let rs = db.executeQuery(queryString, withArgumentsIn: [word]) {
            if rs.next() {
                result = DictionaryEntry(
                    word: rs.string(forColumn: "word") ?? "",
                    translation: rs.string(forColumn: "translation"),
                    phonetic: rs.string(forColumn: "phonetic"),
                    definition: rs.string(forColumn: "definition"),
                    exchange: rs.string(forColumn: "exchange")
                )
                print("[DictionaryDB] 查询成功: \(result?.word ?? "")")
            } else {
                print("[DictionaryDB] 未找到单词: \(word)")
            }
            rs.close()
        } else {
            let errorCode = db.lastErrorCode()
            let errorMessage = db.lastErrorMessage()
            print("[DictionaryDB] 查询失败 - 错误码: \(errorCode), 错误信息: \(errorMessage)")

            // 如果是I/O错误或连接问题，尝试重新连接
            if errorCode == SQLITE_IOERR || errorCode == SQLITE_ERROR || errorCode == SQLITE_BUSY {
                print("[DictionaryDB] 检测到数据库错误，尝试重新连接")
                if reopenDatabase() {
                    print("[DictionaryDB] 重新连接成功，重试查询")
                    return lookup(word)  // 重试一次
                }
            }
        }

        return result
    }
    
    private func openDatabase() -> Bool {
        guard let dbPath = Bundle.main.path(forResource: "ecdict", ofType: "db") else {
            print("[DictionaryDB] 找不到数据库文件")
            return false
        }

        print("[DictionaryDB] 尝试打开数据库: \(dbPath)")

        db = FMDatabase(path: dbPath)

        guard let db = db else {
            print("[DictionaryDB] 数据库对象创建失败")
            return false
        }

        if db.open() {
            print("[DictionaryDB] 数据库连接成功")
            // 设置更安全的数据库参数
            db.executeStatements("PRAGMA journal_mode = WAL")
            db.executeStatements("PRAGMA synchronous = NORMAL")
            db.executeStatements("PRAGMA cache_size = 10000")
            db.executeStatements("PRAGMA temp_store = memory")
            return true
        } else {
            print("[DictionaryDB] 数据库连接失败: \(db.lastErrorMessage())")
            return false
        }
    }

    private func reopenDatabase() -> Bool {
        // 先关闭现有连接
        db?.close()

        // 重新打开数据库
        return openDatabase()
    }
    
    // 添加批量查询方法
    func lookupMultiple(_ words: [String]) -> [DictionaryEntry] {
        var entries: [DictionaryEntry] = []
        
        for word in words {
            if let entry = queryWord(word) {
                entries.append(entry)
            }
        }
        
        return entries
    }
    
    // 添加模糊搜索方法
    func search(_ keyword: String, limit: Int = 10) -> [DictionaryEntry] {
        var entries: [DictionaryEntry] = []
        
        guard let db = db else { return entries }
        
        let queryString = """
            SELECT word, translation, phonetic, definition, exchange 
            FROM stardict 
            WHERE word LIKE ? COLLATE NOCASE
            LIMIT ?
        """
        
        if let rs = db.executeQuery(queryString, withArgumentsIn: [keyword, limit]) {
            while rs.next() {
                let entry = DictionaryEntry(
                    word: rs.string(forColumn: "word") ?? "",
                    translation: rs.string(forColumn: "translation"),
                    phonetic: rs.string(forColumn: "phonetic"),
                    definition: rs.string(forColumn: "definition"),
                    exchange: rs.string(forColumn: "exchange")
                )
                entries.append(entry)
            }
            rs.close()
        }
        
        return entries
    }
    
    private func queryWord(_ word: String) -> DictionaryEntry? {
        guard let db = db else { return nil }
        
        let queryString = """
            SELECT word, translation, phonetic, definition, exchange 
            FROM \(tableName) 
            WHERE word LIKE ? COLLATE NOCASE
        """
        
        if let rs = db.executeQuery(queryString, withArgumentsIn: [word]) {
            defer { rs.close() }
            
            if rs.next() {
                let entry = DictionaryEntry(
                    word: rs.string(forColumn: "word") ?? "",
                    translation: rs.string(forColumn: "translation"),
                    phonetic: rs.string(forColumn: "phonetic"),
                    definition: rs.string(forColumn: "definition"),
                    exchange: rs.string(forColumn: "exchange")
                )
                // print("[DictionaryDB] 查询成功: \(entry)")
                return entry
            } else {
                // print("[DictionaryDB] 未找到单词: \(word)")
            }
        }
        
        return nil
    }
} 
