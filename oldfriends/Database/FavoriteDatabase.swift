import Foundation
import FMDB

enum DatabaseError: Error {
    case databaseNotInitialized
}

class FavoriteDatabase {
    static let shared = FavoriteDatabase()
    private var db: FMDatabase?
    private let currentVersion = 2 // 添加数据库版本号
    
    private init() {
        if let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
            let dbPath = documentsPath.appendingPathComponent("favorites.db").path
            // print("数据库路径: \(dbPath)")
            
            // 如果数据库文件存在，先备份
            if FileManager.default.fileExists(atPath: dbPath) {
                let backupPath = dbPath + ".backup"
                try? FileManager.default.copyItem(atPath: dbPath, toPath: backupPath)
            }
            
            db = FMDatabase(path: dbPath)
            
            if let db = db {
                if db.open() {
                    // 先创建表，再进行迁移
                    do {
                        try createTables()  // 确保先创建表
                        migrateDatabase()   // 然后再进行迁移
                    } catch {
                        // print("创建表失败: \(error)")
                    }
                } else {
                    // print("无法打开收藏数据库")
                }
            }
        }
    }
    
    private func migrateDatabase() {
        // 检查数据库版本
        do {
            // 创建版本表（如果不存在）
            if let database = db {
                try database.executeUpdate("""
                    CREATE TABLE IF NOT EXISTS db_version (
                        version INTEGER PRIMARY KEY
                    );
                """, values: nil)
                
                // 获取当前数据库版本
                var version = 1
                if let rs = database.executeQuery("SELECT version FROM db_version LIMIT 1", withArgumentsIn: []) {
                    if rs.next() {
                        version = Int(rs.int(forColumn: "version"))
                    }
                    rs.close()
                }
                
                // 如果版本低于当前版本，执行迁移
                if version < currentVersion {
                    // 开始事务
                    if database.beginTransaction() {
                        // 备份原有数据
                        var words: [(word: String, createdAt: Date)] = []
                        var sentences: [(sentence: String, createdAt: Date)] = []
                        
                        // 保存现有的单词数据
                        if let rs = database.executeQuery("SELECT word, created_at FROM favorite_words", withArgumentsIn: []) {
                            while rs.next() {
                                if let word = rs.string(forColumn: "word"),
                                   let createdAt = rs.date(forColumn: "created_at") {
                                    words.append((word, createdAt))
                                }
                            }
                            rs.close()
                        }
                        
                        // 保存现有的句子数据
                        if let rs = database.executeQuery("SELECT sentence, created_at FROM favorite_sentences", withArgumentsIn: []) {
                            while rs.next() {
                                if let sentence = rs.string(forColumn: "sentence"),
                                   let createdAt = rs.date(forColumn: "created_at") {
                                    sentences.append((sentence, createdAt))
                                }
                            }
                            rs.close()
                        }
                        
                        // 删除旧表
                        try database.executeUpdate("DROP TABLE IF EXISTS favorite_words", values: nil)
                        try database.executeUpdate("DROP TABLE IF EXISTS favorite_sentences", values: nil)
                        
                        // 创建新表
                        try createTables()
                        
                        // 恢复数据
                        for (word, createdAt) in words {
                            try database.executeUpdate("""
                                INSERT INTO favorite_words (word, created_at)
                                VALUES (?, ?)
                            """, values: [word, createdAt])
                        }
                        
                        for (sentence, createdAt) in sentences {
                            try database.executeUpdate("""
                                INSERT INTO favorite_sentences (sentence, created_at)
                                VALUES (?, ?)
                            """, values: [sentence, createdAt])
                        }
                        
                        // 更新数据库版本
                        try database.executeUpdate("DELETE FROM db_version", values: nil)
                        try database.executeUpdate("INSERT INTO db_version (version) VALUES (?)", values: [currentVersion])
                        
                        // 提交事务
                        if !database.commit() {
                            database.rollback()
                            // print("数据库迁移失败：提交事务失败")
                            return
                        }
                        
                        // print("数据库迁移成功完成")
                    }
                }
            } else {
                throw DatabaseError.databaseNotInitialized
            }
        } catch {
            // 如果发生错误，回滚事务
            if let database = db {
                database.rollback()
            }
            // print("数据库迁移失败: \(error)")
        }
    }
    
    private func createTables() throws {
        // 创建收藏单词表
        let createWordsTable = """
            CREATE TABLE IF NOT EXISTS favorite_words (
                word TEXT PRIMARY KEY,
                season_number INTEGER,
                episode_number INTEGER,
                episode_title TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        
        // 创建收藏句子表
        let createSentencesTable = """
            CREATE TABLE IF NOT EXISTS favorite_sentences (
                sentence TEXT PRIMARY KEY,
                season_number INTEGER,
                episode_number INTEGER,
                episode_title TEXT,
                chinese_text TEXT,
                start_time DOUBLE,
                end_time DOUBLE,
                audio_file_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        
        // 使用 try 直接抛出错误，让调用者处理
        try db?.executeUpdate(createWordsTable, values: nil)
        try db?.executeUpdate(createSentencesTable, values: nil)
    }
    
    func loadFavoriteWords() -> Set<String> {
        var words = Set<String>()
        let query = "SELECT word FROM favorite_words ORDER BY created_at DESC"
        
        if let rs = db?.executeQuery(query, withArgumentsIn: []) {
            while rs.next() {
                if let word = rs.string(forColumn: "word") {
                    words.insert(word)
                }
            }
            rs.close()
        }
        return words
    }
    
    func loadFavoriteSentences() -> [(english: String, chinese: String, seasonNumber: Int, episodeNumber: Int, episodeTitle: String, timestamp: Date, startTime: TimeInterval, endTime: TimeInterval, audioFileName: String)] {
        var sentences: [(english: String, chinese: String, seasonNumber: Int, episodeNumber: Int, episodeTitle: String, timestamp: Date, startTime: TimeInterval, endTime: TimeInterval, audioFileName: String)] = []
        let query = """
            SELECT sentence, chinese_text, season_number, episode_number, episode_title, created_at, start_time, end_time, audio_file_name
            FROM favorite_sentences 
            ORDER BY created_at DESC
        """
        
        if let rs = db?.executeQuery(query, withArgumentsIn: []) {
            while rs.next() {
                if let english = rs.string(forColumn: "sentence"),
                   let chinese = rs.string(forColumn: "chinese_text"),
                   let episodeTitle = rs.string(forColumn: "episode_title"),
                   let timestamp = rs.date(forColumn: "created_at"),
                   let audioFileName = rs.string(forColumn: "audio_file_name") {
                    let seasonNumber = Int(rs.int(forColumn: "season_number"))
                    let episodeNumber = Int(rs.int(forColumn: "episode_number"))
                    let startTime = rs.double(forColumn: "start_time")
                    let endTime = rs.double(forColumn: "end_time")  
                    
                    sentences.append((
                        english: english,
                        chinese: chinese,
                        seasonNumber: seasonNumber,
                        episodeNumber: episodeNumber,
                        episodeTitle: episodeTitle,
                        timestamp: timestamp,
                        startTime: startTime,
                        endTime: endTime,
                        audioFileName: audioFileName
                    ))
                }
            }
            rs.close()
        }
        return sentences
    }
    
    func saveWord(_ word: String, seasonNumber: Int, episodeNumber: Int, episodeTitle: String) {
        // print("[FavoriteDatabase] Attempting to save word: \(word)")
        let query = """
            INSERT OR REPLACE INTO favorite_words 
            (word, season_number, episode_number, episode_title) 
            VALUES (?, ?, ?, ?)
        """
        do {
            try db?.executeUpdate(query, values: [word, seasonNumber, episodeNumber, episodeTitle])
            // print("[FavoriteDatabase] Successfully saved word: \(word)")
        } catch {
            // print("[FavoriteDatabase] Error saving word: \(word), Error: \(error)")
        }
    }
    
    func removeWord(_ word: String) {
        // print("[FavoriteDatabase] Attempting to remove word: \(word)")
        let query = "DELETE FROM favorite_words WHERE word = ?"
        do {
            try db?.executeUpdate(query, values: [word])
            // print("[FavoriteDatabase] Successfully removed word: \(word)")
        } catch {
            // print("[FavoriteDatabase] Error removing word: \(word), Error: \(error)")
        }
    }
    
    func saveSentence(
        _ sentence: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String,
        chineseText: String,
        startTime: TimeInterval,
        endTime: TimeInterval,
        audioFileName: String
    ) {
        // print("[FavoriteDatabase] Attempting to save sentence: \(sentence)")
        let query = """
            INSERT OR REPLACE INTO favorite_sentences 
            (sentence, season_number, episode_number, episode_title, chinese_text, start_time, end_time, audio_file_name) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        do {
            try db?.executeUpdate(query, values: [
                sentence,
                seasonNumber,
                episodeNumber,
                episodeTitle,
                chineseText,
                startTime,
                endTime,
                audioFileName
            ])
            // print("[FavoriteDatabase] Successfully saved sentence: \(sentence)")
        } catch {
            // print("[FavoriteDatabase] Error saving sentence: \(sentence), Error: \(error)")
        }
    }
    
    func removeSentence(_ sentence: String) {
        // print("[FavoriteDatabase] Attempting to remove sentence: \(sentence)")
        let query = "DELETE FROM favorite_sentences WHERE sentence = ?"
        do {
            try db?.executeUpdate(query, values: [sentence])
            // print("[FavoriteDatabase] Successfully removed sentence: \(sentence)")
        } catch {
            // print("[FavoriteDatabase] Error removing sentence: \(sentence), Error: \(error)")
        }
    }
    
    func getWordDetails(_ word: String) -> (seasonNumber: Int, episodeNumber: Int, episodeTitle: String)? {
        let query = "SELECT season_number, episode_number, episode_title FROM favorite_words WHERE word = ?"
        
        if let rs = db?.executeQuery(query, withArgumentsIn: [word]) {
            if rs.next() {
                let seasonNumber = Int(rs.int(forColumn: "season_number"))
                let episodeNumber = Int(rs.int(forColumn: "episode_number"))
                let episodeTitle = rs.string(forColumn: "episode_title") ?? ""
                rs.close()
                return (seasonNumber, episodeNumber, episodeTitle)
            }
            rs.close()
        }
        return nil
    }
    
    func getSentenceDetails(_ sentence: String) -> (seasonNumber: Int, episodeNumber: Int, episodeTitle: String, chineseText: String)? {
        let query = "SELECT season_number, episode_number, episode_title, chinese_text FROM favorite_sentences WHERE sentence = ?"
        
        if let rs = db?.executeQuery(query, withArgumentsIn: [sentence]) {
            if rs.next() {
                let seasonNumber = Int(rs.int(forColumn: "season_number"))
                let episodeNumber = Int(rs.int(forColumn: "episode_number"))
                let episodeTitle = rs.string(forColumn: "episode_title") ?? ""
                let chineseText = rs.string(forColumn: "chinese_text") ?? ""
                rs.close()
                return (seasonNumber, episodeNumber, episodeTitle, chineseText)
            }
            rs.close()
        }
        return nil
    }
    
    func getFavoriteWordEpisode(word: String) -> (seasonNumber: Int, episodeNumber: Int)? {
        let query = """
            SELECT season_number, episode_number 
            FROM favorite_words 
            WHERE word = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        """
        
        var result: (seasonNumber: Int, episodeNumber: Int)?
        
        if let rs = db?.executeQuery(query, withArgumentsIn: [word]) {
            if rs.next() {
                result = (
                    seasonNumber: Int(rs.int(forColumn: "season_number")),
                    episodeNumber: Int(rs.int(forColumn: "episode_number"))
                )
            }
            rs.close()
        }
        
        return result
    }
    
    private func checkTables() {
        let query = "SELECT name FROM sqlite_master WHERE type='table'"
        if let rs = db?.executeQuery(query, withArgumentsIn: []) {
            // print("现有的表:")
            while rs.next() {
//                if let tableName = rs.string(forColumn: "name") {
                    // print("- \(tableName)")
//                }
            }
            rs.close()
        }
    }
    
    deinit {
        db?.close()
    }
} 
