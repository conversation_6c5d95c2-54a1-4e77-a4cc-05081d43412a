//
//  String+WordRange.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import Foundation

// MARK: - NSString Extension
extension NSString {
    /// 获取指定索引位置的单词范围
    /// - Parameter index: 索引位置
    /// - Returns: 单词的范围，如果未找到则返回 NSNotFound
    func rangeOfWord(at index: Int) -> NSRange {
        guard index < length else { return NSRange(location: NSNotFound, length: 0) }
        
        return (self as String).wordRange(at: index) ?? NSRange(location: NSNotFound, length: 0)
    }
}

// MARK: - String Extension
extension String {
    /// 获取指定索引位置的单词范围
    /// - Parameter index: 索引位置
    /// - Returns: 单词的范围，如果未找到则返回 nil
    func wordRange(at index: Int) -> NSRange? {
        guard index < count else { return nil }
        
        let nsString = self as NSString
        let options: NSString.EnumerationOptions = [.byWords, .substringNotRequired]
        var result: NSRange?
        
        nsString.enumerateSubstrings(in: NSRange(location: 0, length: nsString.length), options: options) { substring, substringRange, _, stop in
            if substringRange.contains(index) {
                result = substringRange
                stop.pointee = true
            }
        }
        
        return result
    }
} 