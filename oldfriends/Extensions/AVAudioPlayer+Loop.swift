//
//  AVAudioPlayer+Loop.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import AVFoundation
import Foundation

extension AVAudioPlayer {
    private static let loopTimerKey = UnsafeRawPointer(bitPattern: "loopTimer".hashValue)!
    private static let isLoopingKey = UnsafeRawPointer(bitPattern: "isLooping".hashValue)!
    
    // 将 isLooping 改为 internal 访问级别
    var isLooping: Bool {
        get {
            objc_getAssociatedObject(self, AVAudioPlayer.isLoopingKey) as? Bool ?? false
        }
        set {
            objc_setAssociatedObject(self, AVAudioPlayer.isLoopingKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    private var loopTimer: Timer? {
        get {
            objc_getAssociatedObject(self, AVAudioPlayer.loopTimerKey) as? Timer
        }
        set {
            if let oldTimer = objc_getAssociatedObject(self, AVAudioPlayer.loopTimerKey) as? Timer {
                oldTimer.invalidate()
            }
            objc_setAssociatedObject(self, AVAudioPlayer.loopTimerKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // 停止循环定时器
    func stopLoopTimer() {
        // print("[AVAudioPlayer] 停止循环定时器")
        isLooping = false
        loopTimer?.invalidate()
        loopTimer = nil
    }
    
    // 修改为自定义的循环播放方法
    func playWithLoop(atTime startTime: TimeInterval, toTime endTime: TimeInterval) {
        // print("[AVAudioPlayer] 开始循环播放设置 - 起始时间: \(startTime), 结束时间: \(endTime)")
        
        // 先停止当前的循环
        stopLoopTimer()
        
        // 设置循环播放标记
        isLooping = true
        
        // 设置起始位置并开始播放
        self.currentTime = startTime
        self.play()
        // print("[AVAudioPlayer] 已设置起始位置并开始播放")
        
        // 创建新的定时器来检查是否到达结束时间
        loopTimer = Timer(timeInterval: 0.1, repeats: true) { [weak self] timer in
            guard let self = self else {
                // print("[AVAudioPlayer] Timer 回调 - self 已释放")
                timer.invalidate()
                return
            }
            
            guard self.isLooping else {
                // print("[AVAudioPlayer] Timer 回调 - 循环播放已停止")
                timer.invalidate()
                return
            }
            
            // 如果当前时间超过结束时间，重新开始播放
            if self.currentTime >= endTime {
                // print("[AVAudioPlayer] 循环播放 - 当前时间超过结束时间，重新开始")
                self.currentTime = startTime
                self.play()
                // print("[AVAudioPlayer] 循环播放 - 已重新开始于: \(startTime)")
            }
        }
        
        // 确保定时器被添加到主运行循环
        RunLoop.main.add(loopTimer!, forMode: .common)
        // print("[AVAudioPlayer] 循环定时器已设置并添加到主运行循环")
    }
    
    // 添加一个普通播放方法
    func playNormal() {
        // print("[AVAudioPlayer] 开始普通播放")
        isLooping = false
        self.play()
    }
} 