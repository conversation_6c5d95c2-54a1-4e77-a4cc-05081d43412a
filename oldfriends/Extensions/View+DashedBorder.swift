//
//  View+DashedBorder.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

extension View {
    func dash(phase: CGFloat = 0, lengths: [CGFloat] = [4, 4]) -> some View {
        self.overlay(
            GeometryReader { geometry in
                Path { path in
                    // 使用 addRect 替代 addRoundedRect
                    let rect = CGRect(origin: .zero, size: geometry.size)
                    let cornerRadius: CGFloat = 12
                    
                    // 手动绘制圆角矩形路径
                    path.move(to: CGPoint(x: rect.minX + cornerRadius, y: rect.minY))
                    path.addLine(to: CGPoint(x: rect.maxX - cornerRadius, y: rect.minY))
                    path.addArc(center: CGPoint(x: rect.maxX - cornerRadius, y: rect.minY + cornerRadius),
                               radius: cornerRadius,
                               startAngle: Angle(degrees: -90),
                               endAngle: Angle(degrees: 0),
                               clockwise: false)
                    path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - cornerRadius))
                    path.addArc(center: CGPoint(x: rect.maxX - cornerRadius, y: rect.maxY - cornerRadius),
                               radius: cornerRadius,
                               startAngle: Angle(degrees: 0),
                               endAngle: Angle(degrees: 90),
                               clockwise: false)
                    path.addLine(to: CGPoint(x: rect.minX + cornerRadius, y: rect.maxY))
                    path.addArc(center: CGPoint(x: rect.minX + cornerRadius, y: rect.maxY - cornerRadius),
                               radius: cornerRadius,
                               startAngle: Angle(degrees: 90),
                               endAngle: Angle(degrees: 180),
                               clockwise: false)
                    path.addLine(to: CGPoint(x: rect.minX, y: rect.minY + cornerRadius))
                    path.addArc(center: CGPoint(x: rect.minX + cornerRadius, y: rect.minY + cornerRadius),
                               radius: cornerRadius,
                               startAngle: Angle(degrees: 180),
                               endAngle: Angle(degrees: 270),
                               clockwise: false)
                }
                .stroke(style: StrokeStyle(
                    lineWidth: 1,
                    lineCap: .round,
                    lineJoin: .round,
                    dash: lengths,
                    dashPhase: phase
                ))
            }
        )
    }
} 