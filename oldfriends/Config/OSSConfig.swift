import Foundation
import os

struct OSSConfig {
    // 添加Logger
    private static let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "OSSConfig")
    
    // 获取OSS配置
    static var accessKeyId: String {
        let keyId = Bundle.main.object(forInfoDictionaryKey: "OSS_ACCESS_KEY_ID") as? String ?? ""
        if keyId.isEmpty {
            logger.error("OSS_ACCESS_KEY_ID 未在Info.plist中配置或为空")
        } else {
            logger.info("成功获取 OSS_ACCESS_KEY_ID")
        }
        return keyId
    }
    
    static var accessKeySecret: String {
        let secret = Bundle.main.object(forInfoDictionaryKey: "OSS_ACCESS_KEY_SECRET") as? String ?? ""
        if secret.isEmpty {
            logger.error("OSS_ACCESS_KEY_SECRET 未在Info.plist中配置或为空")
        } else {
            logger.info("成功获取 OSS_ACCESS_KEY_SECRET")
        }
        return secret
    }
    
    static var bucket: String {
        let bucketName = Bundle.main.object(forInfoDictionaryKey: "OSS_BUCKET") as? String ?? ""
        if bucketName.isEmpty {
            logger.error("OSS_BUCKET 未在Info.plist中配置或为空")
        } else {
            logger.info("成功获取 OSS_BUCKET: \(bucketName)")
        }
        return bucketName
    }
    
    static var endpoint: String {
        let endpointUrl = Bundle.main.object(forInfoDictionaryKey: "OSS_ENDPOINT") as? String ?? ""
        if endpointUrl.isEmpty {
            logger.error("OSS_ENDPOINT 未在Info.plist中配置或为空")
        } else {
            logger.info("成功获取 OSS_ENDPOINT: \(endpointUrl)")
        }
        return endpointUrl
    }
    
    // 构建基础URL
    static var baseURL: String {
        let url = "https://\(bucket).\(endpoint)"
        logger.info("构建OSS基础URL: \(url)")
        return url
    }
    
    // 初始化时检查配置
    static func checkConfiguration() {
        logger.info("===== 检查OSS配置 =====")
        _ = accessKeyId
        _ = accessKeySecret
        _ = bucket
        _ = endpoint
        _ = baseURL
        logger.info("OSS配置检查完成")
    }
}