import Foundation
import SwiftUI
import os

class DownloadViewModel: ObservableObject {
    // 添加Logger
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "DownloadViewModel")
    
    // 下载状态相关属性
    @Published var isDownloading: Bool = false {
        didSet {
            if !isDownloading {
                // 下载完成时清除缓存
                DownloadStatusCache.shared.clear()
            }
        }
    }
    @Published var downloadProgress: Double = 0
    @Published var currentDownloadingFile: String = ""
    
    // 单例模式
    static let shared = DownloadViewModel()
    
    private init() {}
    
    // MARK: - Debug Logger
    private func debugLog(_ message: String, type: OSLogType = .info) {
        #if DEBUG
        switch type {
        case .info:
            logger.info("\(message)")
        case .error:
            logger.error("\(message)")
        case .debug:
            logger.debug("\(message)")
        case .fault:
            logger.fault("\(message)")
        default:
            logger.info("\(message)")
        }
        #endif
    }
    
    // 开始下载剧集资源
    func startDownload(
        audioName: String,
        subtitleName: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String,
        completion: @escaping () -> Void
    ) {
        debugLog("===== 开始下载剧集资源 =====")
        debugLog("- 音频: \(audioName)")
        debugLog("- 字幕: \(subtitleName)")
        debugLog("- 季数: \(seasonNumber)")
        debugLog("- 集数: \(episodeNumber)")
        
        isDownloading = true
        currentDownloadingFile = ""
        downloadProgress = 0
        
        let audioFileName = "\(audioName).mp3"
        let subtitleFileName = "\(subtitleName).srt"
        
        // 下载音频文件
        if !DownloadManager.shared.isFileDownloaded(audioFileName) {
            debugLog("开始下载音频文件: \(audioFileName)")
            currentDownloadingFile = audioFileName
            DownloadManager.shared.startDownload(
                fileName: audioFileName,
                fileType: "audio",
                progressHandler: { [weak self] progress in
                    DispatchQueue.main.async {
                        self?.downloadProgress = progress
                        self?.debugLog("下载进度更新: \(progress * 100)%")
                    }
                },
                completion: { [weak self] in
                    self?.debugLog("音频文件下载完成: \(audioFileName)")
                    self?.checkDownloadCompletion(
                        audioFileName: audioFileName,
                        subtitleFileName: subtitleFileName,
                        completion: completion
                    )
                }
            )
        } else {
            debugLog("音频文件已存在，跳过下载: \(audioFileName)")
        }
        
        // 下载字幕文件
        if !DownloadManager.shared.isFileDownloaded(subtitleFileName) {
            debugLog("开始下载字幕文件: \(subtitleFileName)")
            if currentDownloadingFile.isEmpty {
                currentDownloadingFile = subtitleFileName
            }
            DownloadManager.shared.startDownload(
                fileName: subtitleFileName,
                fileType: "srt",
                progressHandler: { [weak self] progress in
                    if self?.currentDownloadingFile == subtitleFileName {
                        DispatchQueue.main.async {
                            self?.downloadProgress = progress
                            self?.debugLog("字幕下载进度更新: \(progress * 100)%")
                        }
                    }
                },
                completion: { [weak self] in
                    self?.debugLog("字幕文件下载完成: \(subtitleFileName)")
                    self?.checkDownloadCompletion(
                        audioFileName: audioFileName,
                        subtitleFileName: subtitleFileName,
                        completion: completion
                    )
                }
            )
        } else {
            debugLog("字幕文件已存在，跳过下载: \(subtitleFileName)")
        }
    }
    
    // 检查下载完成状态
    private func checkDownloadCompletion(
        audioFileName: String,
        subtitleFileName: String,
        completion: @escaping () -> Void
    ) {
        debugLog("检查下载完成状态")
        debugLog("- 音频文件已下载: \(DownloadManager.shared.isFileDownloaded(audioFileName))")
        debugLog("- 字幕文件已下载: \(DownloadManager.shared.isFileDownloaded(subtitleFileName))")
        
        if DownloadManager.shared.isFileDownloaded(audioFileName) &&
           DownloadManager.shared.isFileDownloaded(subtitleFileName) {
            debugLog("所有文件下载完成，重置下载状态")
            DispatchQueue.main.async { [weak self] in
                self?.isDownloading = false
                self?.downloadProgress = 0
                self?.currentDownloadingFile = ""
                completion()
            }
        } else {
            debugLog("部分文件未完成下载，等待继续...", type: .error)
        }
    }
    
    // 取消下载
    func cancelDownload() {
        debugLog("用户取消下载")
        isDownloading = false
        downloadProgress = 0
        currentDownloadingFile = ""
        DownloadManager.shared.cancelDownload()
    }
    
    // 检查资源文件是否完整
    func areResourceFilesComplete(audioName: String, subtitleName: String) -> Bool {
        let audioFileName = "\(audioName).mp3"
        let subtitleFileName = "\(subtitleName).srt"
        
        let audioValid = DownloadManager.shared.validateFile(fileName: audioFileName)
        let subtitleValid = DownloadManager.shared.validateFile(fileName: subtitleFileName)
        
        debugLog("检查资源文件完整性:")
        debugLog("- 音频文件 \(audioFileName): \(audioValid ? "有效" : "无效")")
        debugLog("- 字幕文件 \(subtitleFileName): \(subtitleValid ? "有效" : "无效")")
        
        return audioValid && subtitleValid
    }
    
    // 重新下载资源
    func redownload(
        audioName: String,
        subtitleName: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String,
        completion: @escaping () -> Void
    ) {
        debugLog("开始重新下载资源")
        // 开始重新下载
        startDownload(
            audioName: audioName,
            subtitleName: subtitleName,
            seasonNumber: seasonNumber,
            episodeNumber: episodeNumber,
            episodeTitle: episodeTitle,
            completion: completion
        )
    }
} 