//
//  PlayerViewModel.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI
import AVFoundation
import MediaPlayer
import StoreKit
import StoreHelper
import os.log

// AB 循环状态枚举
enum ABLoopState: String {
    case notSet = "notSet"        // 未设置
    case settingA = "settingA"    // 正在设置 A 点
    case settingB = "settingB"    // 正在设置 B 点
    case looping = "looping"      // 循环播放中
}

// 字幕显示模式枚举
enum SubtitleDisplayMode {
    case chineseOnly    // 仅中文
    case englishOnly    // 仅英文
    case both          // 中英文
    case none          // 不显示
}

// 模拟视频播放状态管理
// 模拟视频播放状态管理
class PlayerViewModel: ObservableObject {
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "PlayerViewModel")
    
    @Published var isPlaying = false
    @Published var currentTime: TimeInterval = 0
    @Published var totalTime: TimeInterval = 0
    @Published var playbackSpeed: Double = 1.0
    
    // 初始化为空数组，从文件加载后再填充
    var subtitles: [Subtitle] = []
    private var audioPlayer: AVAudioPlayer?
    
    // 添加 StoreHelper 环境对象
    private var storeHelper: StoreHelper?
    
    func setStoreHelper(_ helper: StoreHelper) {
        self.storeHelper = helper
    }
    
    // 添加定时器来更新播放进度
    private var timer: Timer?
    // 添加保存进度定时器
    private var saveProgressTimer: Timer?
    
    // AB循环起止点
    private(set) var abLoopStartTime: TimeInterval?
    private(set) var abLoopEndTime: TimeInterval?
    
    // 添加缺失的属性
    @Published var currentSeasonNumber: Int = 1
    @Published var currentEpisodeNumber: Int = 1
    @Published var currentEpisodeTitle: String = "领航员"
    
    // 添加当前音频文件名属性
    @Published var currentAudioName: String = ""
    
    @Published var subtitleDisplayMode: SubtitleDisplayMode = .both {
        willSet {
            // print("[PlayerViewModel] 字幕显示模式即将变更: \(subtitleDisplayMode) -> \(newValue)")
        }
        didSet {
            // print("[PlayerViewModel] 字幕显示模式已变更: \(oldValue) -> \(subtitleDisplayMode)")
            // 强发送更新通知
            objectWillChange.send()
        }
    }
    
    @Published var currentTheme: AppTheme = .light {
        didSet {
            logger.info("主题设置变化: \(oldValue == .dark ? "深色" : "浅色") -> \(self.currentTheme == .dark ? "深色" : "浅色")")
            logger.info("isThemeManuallySet: \(self.isThemeManuallySet)")
            // 保存主题设置
            UserDefaults.standard.set(self.currentTheme == .dark ? "dark" : "light", forKey: "app_theme")
            UserDefaults.standard.set(self.isThemeManuallySet, forKey: "theme_manually_set")
        }
    }
    @Published var isThemeManuallySet: Bool = false {
        didSet {
            logger.info("主题手动设置状态变化: \(oldValue) -> \(self.isThemeManuallySet)")
        }
    }
    
    @Published var playMode: PlayMode = .sequential {
        didSet {
            // print("[PlayMode] 播放模式切换: \(oldValue) -> \(playMode)")
            if playMode == .abLoop {
                // 切换到 AB 循环模式时，自动启用设置功能并设置初始状态
                // print("[PlayMode] 切换到AB循环模式")
                abLoopState = .settingA
                abLoopStartTime = nil
                abLoopEndTime = nil
                isABLoopSettingEnabled = true  // 确保启用设置
                // print("[PlayMode] AB循环设置已启用")
                
                // 暂停当前播放
                if isPlaying {
                    isPlaying = false
                    audioPlayer?.pause()
                }
                // 停止任何现有的循环
                audioPlayer?.stopLoopTimer()
            } else if playMode == .singleSentence {
                // 切换到单句循环模式时，立即处理当前字幕
                if let currentSubtitle = getCurrentSubtitle() {
                    // print("[PlayMode] 切换到单句循环模式 - 当前字幕: \(currentSubtitle.english)")
                    playSubtitle(currentSubtitle)
                } else if let nearestSubtitle = getNearestSubtitle() {
                    // 如果没有当前字幕，使用最近的字幕
                    // print("[PlayMode] 切换到单句循环模式 - 使用最近字幕: \(nearestSubtitle.english)")
                    playSubtitle(nearestSubtitle)
                } else {
                    // print("[PlayMode] 切换到单句循环模式 - 无可用字幕")
                    if isPlaying {
                        isPlaying = false
                        audioPlayer?.pause()
                    }
                }
            } else {
                // 切换到其他模式时，清除 AB 循环状态
                abLoopState = .notSet
                abLoopStartTime = nil
                abLoopEndTime = nil
                isABLoopSettingEnabled = false
                // 停止任何现有的循环
                audioPlayer?.stopLoopTimer()
            }
        }
    }
    
   
    
    // 添加切换字幕显示模式的方法
    func setSubtitleDisplayMode(_ mode: SubtitleDisplayMode) {
        // print("[PlayerViewModel] setSubtitleDisplayMode 被调用: \(subtitleDisplayMode) -> \(mode)")
        // 直接在主线程更新状态
        subtitleDisplayMode = mode
    }
    
    func toggleTheme() {
        currentTheme = currentTheme == .dark ? .light : .dark
        isThemeManuallySet = true  // 标记主题已被手动设置
        // 保存到 UserDefaults
        UserDefaults.standard.set(currentTheme == .dark ? "dark" : "light", forKey: "app_theme")
        UserDefaults.standard.set(true, forKey: "theme_manually_set")
        // 主题切换时发送通知
        objectWillChange.send()
    }
    
    // 更新播放进度
    func seek(to time: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        currentTime = min(max(0, time), totalTime)
        player.currentTime = currentTime
        
        // 更新远程控制中心信息
        remoteControlManager?.updateNowPlayingInfo()
    }
    
    // 添加缓存上一次查找的索引
    private var lastSubtitleIndex: Int = 0
    private var lastUpdateTime: TimeInterval = 0
    
    // 添加当前字幕状态
    @Published private(set) var currentSubtitle: Subtitle?
    
    // 更新 getCurrentSubtitle 方法
    func getCurrentSubtitle() -> Subtitle? {
        return currentSubtitle
    }
    
    // 优化字幕更新逻辑
    private func updateCurrentSubtitle() {
        // 如果当前时间和上次更新时间相差不大，直接返回
        if abs(currentTime - lastUpdateTime) < 0.05 {
            return
        }
        lastUpdateTime = currentTime

        // 检查字幕数组是否为空
        guard !subtitles.isEmpty else {
            print("[字幕更新] ⚠️ 字幕数组为空，无法更新字幕")
            currentSubtitle = nil
            return
        }

        print("[字幕更新] 🔍 查找时间 \(currentTime) 对应的字幕，总字幕数: \(subtitles.count)")

        // 如果当前字幕仍然有效,直接返回
        if let current = currentSubtitle,
           currentTime >= current.startTime && currentTime < current.endTime {
            print("[字幕更新] ✅ 当前字幕仍然有效: \(current.english)")
            print("[字幕更新] 📊 字幕时间范围: \(current.startTime) - \(current.endTime) 秒")
            return
        }

        // 二分查找最近的字幕
        var left = 0
        var right = subtitles.count - 1

        while left <= right {
            let mid = (left + right) / 2
            let subtitle = subtitles[mid]

            if currentTime >= subtitle.startTime && currentTime < subtitle.endTime {
                lastSubtitleIndex = mid
                currentSubtitle = subtitle
                print("[字幕更新] 🎯 找到匹配字幕[索引\(mid)]: \(subtitle.english)")
                return
            } else if currentTime < subtitle.startTime {
                right = mid - 1
            } else {
                left = mid + 1
            }
        }

        // 如果没找到完全匹配的，使用最近的字幕
        if left < subtitles.count {
            lastSubtitleIndex = left
            currentSubtitle = subtitles[left]
            print("[字幕更新] 📍 使用最近字幕[左侧索引\(left)]: \(subtitles[left].english)")
        } else if right >= 0 {
            lastSubtitleIndex = right
            currentSubtitle = subtitles[right]
            print("[字幕更新] 📍 使用最近字幕[右侧索引\(right)]: \(subtitles[right].english)")
        } else {
            currentSubtitle = nil
            print("[字幕更新] ❌ 未找到任何匹配的字幕")
        }
    }
    
    // 添加字幕滚动控制相关的属性
    @Published var shouldScrollToSubtitle = false
    @Published var visibleSubtitleRange: Range<Int>? = nil
    private var lastScrolledSubtitleIndex: Int = 0
    
    // 检查字幕是否需要滚动
    func checkIfNeedScroll() {
        // 只要有当前字幕就触发滚动
        shouldScrollToSubtitle = currentSubtitle != nil
    }
    
    // 更新可视范围
    func updateVisibleRange(start: Int, end: Int) {
        visibleSubtitleRange = start..<end
    }
    
    // 开始播放时启动定时器
    private func startProgressTimer() {
        stopProgressTimer()  // 确保先停止之前的定时器

        print("[定时器] 🚀 启动进度更新定时器")

        // 高亮更新定时器 - 0.5秒更新一次
        timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            guard let self = self,
                  let player = self.audioPlayer,
                  self.isPlaying else {
                print("[定时器] ⚠️ 定时器条件不满足 - player: \(self?.audioPlayer != nil), isPlaying: \(self?.isPlaying ?? false)")
                return
            }

            // 更新当前时间和字幕
            let oldSubtitle = self.currentSubtitle
            let oldTime = self.currentTime
            self.currentTime = player.currentTime

            // 添加调试信息
            if abs(oldTime - self.currentTime) > 0.1 {
                print("[定时器] ⏰ 时间更新: \(oldTime) -> \(self.currentTime)")
            }

            self.updateCurrentSubtitle()

            // 如果字幕发生变化，更新锁屏信息
            if oldSubtitle?.id != self.currentSubtitle?.id {
                print("[定时器] 📝 字幕变化: \(oldSubtitle?.english ?? "nil") -> \(self.currentSubtitle?.english ?? "nil")")
                self.remoteControlManager?.updateNowPlayingInfo()
            }
            
            // 检查播放控制逻辑
            if self.playMode == .abLoop,
               let endTime = self.abLoopEndTime,
               player.currentTime >= endTime {
                if let startTime = self.abLoopStartTime {
                    self.seek(to: startTime)
                    player.play()
                }
            }
            
            if self.playMode == .singleSentence,
               let currentSubtitle = self.getCurrentSubtitle(),
               player.currentTime >= currentSubtitle.endTime {
                self.playSubtitle(currentSubtitle)
            }
            
            if !player.isPlaying {
                self.handlePlaybackFinished()
            }
        }
        
        // 滚动检查定时器 - 0.5秒检查一次
        scrollCheckTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            guard let self = self,
                  self.isPlaying else {
                return
            }
            
            // 检查是否需要滚动
            self.checkIfNeedScroll()
            
            // 更新锁屏信息（每0.5秒更新一次进度）
            self.remoteControlManager?.updateNowPlayingInfo()
        }
        
        // 启动独立的保存进度定时器
        startSaveProgressTimer()
    }
    
    // 添加滚动检查定时器属性
    private var scrollCheckTimer: Timer?
    
    // 修改停止定时器的方法
    private func stopProgressTimer() {
        print("[定时器] 🛑 停止进度更新定时器")

        timer?.invalidate()
        timer = nil

        scrollCheckTimer?.invalidate()
        scrollCheckTimer = nil

        saveProgressTimer?.invalidate()
        saveProgressTimer = nil
    }
    
    // 添加启动保存进度定时器的方法
    private func startSaveProgressTimer() {
        saveProgressTimer?.invalidate()
        saveProgressTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.saveLastPlayedInfo()
        }
        RunLoop.main.add(saveProgressTimer!, forMode: .common)
    }
    
    // 加载资源文件
    func loadEpisode(
        audioName: String,
        subtitleName: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String
    ) {
        print("[PlayerViewModel] 开始加载剧集:")
        print("- 季数: \(seasonNumber)")
        print("- 集数: \(episodeNumber)")
        print("- 音频: \(audioName)")
        print("- 标题: \(episodeTitle)")
        
        Task {
            if !( MembershipManager.shared.canPlayEpisode(seasonNumber: seasonNumber, episodeNumber: episodeNumber)) {
                pendingEpisode = (audioName, subtitleName, seasonNumber, episodeNumber, episodeTitle)
                await MainActor.run {
                    MembershipManager.shared.showPurchaseAlert = true
                }
                return
            }

            await MainActor.run {
                // 保存当前状态，以便在清理时使用
                let oldSeasonNumber = currentSeasonNumber
                let oldEpisodeNumber = currentEpisodeNumber
            
                
                // 只在确认可以播放新剧集，且新剧集与当前播放不同时才清理
                if oldSeasonNumber != seasonNumber || oldEpisodeNumber != episodeNumber {
                    cleanup()
                }
                
                // 设置新剧集信息
                self.currentAudioName = audioName
                self.currentEpisodeTitle = episodeTitle
                self.currentEpisodeNumber = episodeNumber
                self.currentSeasonNumber = seasonNumber
                
                if remoteControlManager == nil {
                    remoteControlManager = RemoteControlManager(playerViewModel: self)
                }
                
                // 如果是第一季第一集，直接从 Bundle 加载
                if seasonNumber == 1 && episodeNumber == 1 {
                    loadFiles(audioName: audioName,
                            subtitleName: subtitleName,
                            seasonNumber: seasonNumber,
                            episodeNumber: episodeNumber,
                            episodeTitle: episodeTitle)
                    return
                }
                
                // 检查是否需要下载
                let needsDownload = !DownloadManager.shared.isFileDownloaded("\(audioName).mp3") ||
                                    !DownloadManager.shared.isFileDownloaded("\(subtitleName).srt")
                
                if needsDownload {
                    downloadViewModel.startDownload(
                        audioName: audioName,
                        subtitleName: subtitleName,
                        seasonNumber: seasonNumber,
                        episodeNumber: episodeNumber,
                        episodeTitle: episodeTitle
                    ) { [weak self] in
                        self?.loadFiles(
                            audioName: audioName,
                            subtitleName: subtitleName,
                            seasonNumber: seasonNumber,
                            episodeNumber: episodeNumber,
                            episodeTitle: episodeTitle
                        )
                    }
                } else {
                    loadFiles(
                        audioName: audioName,
                        subtitleName: subtitleName,
                        seasonNumber: seasonNumber,
                        episodeNumber: episodeNumber,
                        episodeTitle: episodeTitle
                    )
                }
                
                remoteControlManager?.updateNowPlayingInfo()
            }
        }
    }
    
    // 将原来的文件加载逻辑抽取到单独的方法
    private func loadFiles(audioName: String,
                         subtitleName: String,
                         seasonNumber: Int,
                         episodeNumber: Int,
                         episodeTitle: String) {
        // print("[音频加载] ===== 开始加载文件 =====")
        // print("- 音频: \(audioName)")
        // print("- 字幕: \(subtitleName)")
        // print("- 季数: \(seasonNumber)")
        // print("- 集数: \(episodeNumber)")
        // print("- 标题: \(episodeTitle)")
        
        // 加载音频
        var audioURL: URL?
        
        // 特殊处理第一季第一集
        if seasonNumber == 1 && episodeNumber == 1 {
            // print("[音频加载] 正在处理第一季第一集")
            // 直接从 Bundle 根目录加载
            if let bundleURL = Bundle.main.url(forResource: "S01E01", withExtension: "mp3") {
                audioURL = bundleURL
                // print("[特殊处理] 从 Bundle 加载音频: \(bundleURL)")
                // print("[特殊处理] Bundle 路径: \(Bundle.main.bundlePath)")
            } else {
                // print("[特殊处理] 未在 Bundle 中找到音频文件，尝试加载 Documents 目录加载")
                // print("[特殊处理] 查找的资源名: S01E01.mp3")
                // print("[特殊处理] 查找的子目录: Audios")
                audioURL = DownloadManager.shared.getLocalFileURL(for: "\(audioName).mp3")
//                if let docURL = audioURL {
//                     print("[特殊处理] Documents 目录中找到文件: \(docURL.path)")
//                } else {
//                     print("[特殊处理] Documents 目录中也未找到文件")
//                }
            }
        } else {
            // 其他剧集使用原有逻辑
            // print("[音频加载] 加载第\(seasonNumber)季第\(episodeNumber)集")
            audioURL = DownloadManager.shared.getLocalFileURL(for: "\(audioName).mp3")
            
            // 验证文件并在需要时触发重新下载
            let audioFileName = "\(audioName).mp3"
            let subtitleFileName = "\(subtitleName).srt"
            
            // 验证音频文件
            if let audioURL = DownloadManager.shared.getLocalFileURL(for: audioFileName) {
                do {
                    let audioAttrs = try FileManager.default.attributesOfItem(atPath: audioURL.path)
                    if (audioAttrs[.size] as? Int64 ?? 0) <= 0 {
                        // print("[音频加载] 音频文件无效，触发重新下载")
                        DownloadManager.shared.startDownload(fileName: audioFileName, fileType: "audio")
                        return
                    }
                } catch {
                    // print("[音频加载] 音频文件验证失败，触发重新下载: \(error)")
                    DownloadManager.shared.startDownload(fileName: audioFileName, fileType: "audio")
                    return
                }
            }
            
            // 验证字幕文件
            if let subtitleURL = DownloadManager.shared.getLocalFileURL(for: subtitleFileName) {
                do {
                    let content = try String(contentsOf: subtitleURL, encoding: .utf8)
                    if content.isEmpty || content.contains("<Error>") || content.contains("<Code>") {
                        // print("[字幕加载] 字幕文件无效，触发重新下载")
                        DownloadManager.shared.startDownload(fileName: subtitleFileName, fileType: "srt")
                        return
                    }
                } catch {
                    // print("[字幕加载] 字幕文件验证失败，触发重新下载: \(error)")
                    DownloadManager.shared.startDownload(fileName: subtitleFileName, fileType: "srt")
                    return
                }
            }
            
            // 继续原有的加载逻辑
            audioURL = DownloadManager.shared.getLocalFileURL(for: audioFileName)
//            if let url = audioURL {
//                 print("[音频加载] 找到音频文件: \(url.path)")
//            } else {
//                 print("[音频加载] 未找到音频文件: \(audioFileName)")
//            }
        }
        
        if let audioURL = audioURL {
            // print("[音频加载] 文件路径: \(audioURL)")
            do {
                let player = try AVAudioPlayer(contentsOf: audioURL)
                player.rate = Float(playbackSpeed)  // 设置初始速度
                player.enableRate = true
                audioPlayer = player
                totalTime = player.duration
                // print("[音频加载] 加载成功，时长: \(totalTime)秒")
                
                // 检查是否需要恢复上次播放位置
                if let lastPlayed = loadLastPlayedInfo(),
                   let lastSeasonNumber = lastPlayed["seasonNumber"] as? Int,
                   let lastEpisodeNumber = lastPlayed["episodeNumber"] as? Int,
                   let lastTime = lastPlayed["currentTime"] as? TimeInterval,
                   lastSeasonNumber == seasonNumber && lastEpisodeNumber == episodeNumber {
                    // print("[音频加载] 恢复上次播放位置: \(lastTime)")
                    currentTime = lastTime
                    player.currentTime = lastTime
                } else {
                    // print("[音频加载] 从头开始播放")
                    currentTime = 0
                    player.currentTime = 0
                }
            } catch {
                // print("[音频加载] 加载失败：\(error)")
            }
        } else {
            // print("[音频加载] 未找到音频文件：\(audioName).mp3")
        }
        
        // 加载字幕
        var subtitleURL: URL?
        
        // 特殊处理第一季第一集
        if seasonNumber == 1 && episodeNumber == 1 {
            // 尝试从 Bundle 的 Subtitles 目录加载
            if let bundleURL = Bundle.main.url(forResource: "Subtitles/S01E01", withExtension: "srt") {
                subtitleURL = bundleURL
                print("[特殊处理] 从 Bundle Subtitles 目录加载字幕: \(bundleURL)")
            } else if let bundleURL = Bundle.main.url(forResource: "S01E01", withExtension: "srt") {
                subtitleURL = bundleURL
                print("[特殊处理] 从 Bundle 根目录加载字幕: \(bundleURL)")
            } else {
                print("[特殊处理] 未在 Bundle 中找到字幕文件，尝试从 Documents 目录加载")
                subtitleURL = DownloadManager.shared.getLocalFileURL(for: "\(subtitleName).srt")
                if let docURL = subtitleURL {
                    print("[特殊处理] 从 Documents 目录加载字幕: \(docURL)")
                } else {
                    print("[特殊处理] Documents 目录中也未找到字幕文件")
                }
            }
        } else {
            // 其他剧集使用原有逻辑
            subtitleURL = DownloadManager.shared.getLocalFileURL(for: "\(subtitleName).srt")
        }
        
        if let subtitleURL = subtitleURL {
            print("[字幕加载] 开始加载字幕文件: \(subtitleURL.path)")
            do {
                let content = try String(contentsOf: subtitleURL, encoding: .utf8)
                print("[字幕加载] 文件内容长度: \(content.count) 字符")
                print("[字幕加载] 文件内容前200字符: \(String(content.prefix(200)))")
                subtitles = SubtitleParser.parseSubtitles(from: content)
                print("[字幕加载] 成功解析字幕数量: \(subtitles.count)")

                // 打印前几个字幕用于调试
                if subtitles.count > 0 {
                    print("[字幕加载] 第一个字幕: \(subtitles[0].english) | \(subtitles[0].chinese)")
                }
                if subtitles.count > 1 {
                    print("[字幕加载] 第二个字幕: \(subtitles[1].english) | \(subtitles[1].chinese)")
                }

                // 更新当前字幕
                updateCurrentSubtitle()

                // 设置加载完成标记
                hasLoaded = true
                // 触发滚动到当前字幕
                shouldScrollToSubtitle = true

                print("[字幕加载] 加载完成，当前字幕已更新")
            } catch {
                print("[字幕加载] 加载失败: \(error)")
            }
        } else {
            print("[字幕加载] 未找到字幕文件: \(subtitleName).srt")
        }
        
        // print("[音频加载] ===== 加载完成 =====")
    }
    
    
    
  
   
    

    

   
    
    // 格式化时间
    func formatTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) / 60 % 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    // 播放指定字幕
    func playSubtitle(_ subtitle: Subtitle) {
        let startTime = Date()
        
        guard let player = audioPlayer else { return }
        
        // print("[PlaySubtitle] 开始播放字幕 - 模式: \(playMode)")
        // print("[PlaySubtitle] 字幕内容: \(subtitle.english)")
        
        // 如果是 AB 循环模式且已设置了循环点，则不允许切换字幕
        if playMode == .abLoop && abLoopStartTime != nil {
            return
        }
        
        // 定位到新字幕的起始时间
        currentTime = subtitle.startTime
        
        // 根据不同的播放模式处理
        switch playMode {
        case .singleSentence:
            // print("[PlaySubtitle] 单句循环模式 - 设置时间范围: \(subtitle.startTime) -> \(subtitle.endTime)")
            // 先停止当前的播放和定时器
            player.stop()
            player.stopLoopTimer()  // 只在循环模式下调用 stopLoopTimer
            // 使用循环播放
            player.playWithLoop(atTime: subtitle.startTime, toTime: subtitle.endTime)
            isPlaying = true
            // print("[PlaySubtitle] 单句循环模式 - 已开始循环播放")
            
        default:
            // print("[PlaySubtitle] 普通播放模式")
            if player.isLooping {  // 只有在之前是循环模式时才需要停止循环定时器
                player.stopLoopTimer()
            }
            player.currentTime = subtitle.startTime
            player.play()
            isPlaying = true
        }
        
        startProgressTimer()
        remoteControlManager?.updateNowPlayingInfo()
        
        let timeElapsed = Date().timeIntervalSince(startTime)
        if timeElapsed > 0.05 {
            // print("[性能] 播放字幕操作耗时: \(timeElapsed)秒")
        }
    }
    
    // 播放/暂停控制
    func togglePlayPause() {
        guard let player = audioPlayer else {
            print("[播放控制] ❌ 音频播放器为空")
            return
        }

        isPlaying.toggle()
        print("[播放控制] 🎵 切换播放状态: \(isPlaying ? "播放" : "暂停")")

        if isPlaying {
            player.play()
            startProgressTimer()
            print("[播放控制] ▶️ 开始播放，当前时间: \(currentTime)")
        } else {
            player.pause()
            stopProgressTimer()
            print("[播放控制] ⏸️ 暂停播放")
        }

        // 更新远程控制中心信息
        remoteControlManager?.updateNowPlayingInfo()
    }
    
    // 添调试法
    func debugPrintBundleContents() {
//        if let resourcePath = Bundle.main.resourcePath {
//             print("Bundle 资源路径：\(resourcePath)")
//            do {
//                let contents = try FileManager.default.contentsOfDirectory(atPath: resourcePath)
//                 print("Bundle 中的文件\(contents)")
//            } catch {
//                 print("读目录失败：\(error)")
//            }
//        }
    }
    
    // 设置播放速度
    func setPlaybackSpeed(_ speed: Double) {
        // print("[PlaybackSpeed] 设置播放速度: \(speed)")
        playbackSpeed = speed
        if let player = audioPlayer {
            player.rate = Float(speed)
            // print("[PlaybackSpeed] 已更新播放器速度: \(player.rate)")
            // 如果当前是暂停状态，暂停
            if !isPlaying {
                // print("[PlaybackSpeed] 当前为暂停状态，保持暂停")
                player.pause()
            }
            // 更新锁屏信息
            remoteControlManager?.updateNowPlayingInfo()
        } else {
            // print("[PlaybackSpeed] 播放器未初始化，无法设置速度")
        }
    }
    
    // 添加调试方来检查幕是否正确加载
    func debugSubtitles() {
        // print("载的字幕数: \(subtitles.count)")
//        if let first = subtitles.first {
//             print("第一条字幕: \(first.english) - \(first.chinese)")
//             print("开始时间: \(first.startTime), 结束时间: \(first.endTime)")
//        }
    }
    
    // 添加清理方法
    public func cleanup() {
        // 添加状态检查，避免重复清理
        guard audioPlayer != nil || isPlaying || hasLoaded else {
            print("[PlayerViewModel] ⚠️ 跳过不必要的清理")
            return
        }
        
        print("[PlayerViewModel] ===== 开始清理资源 =====")
        print("[PlayerViewModel] 当前播放状态:")
        print("- 季数: \(currentSeasonNumber)")
        print("- 集数: \(currentEpisodeNumber)")
        print("- 音频: \(currentAudioName)")
        print("- 标题: \(currentEpisodeTitle)")
        print("- 当前时间: \(currentTime)")
        
        // 保存当前状态的副本
        let lastSeasonNumber = currentSeasonNumber
        let lastEpisodeNumber = currentEpisodeNumber
        let lastAudioName = currentAudioName
        let lastEpisodeTitle = currentEpisodeTitle
        let lastCurrentTime = currentTime
        
        // 先停止播放和所有计时器
        stopProgressTimer() // 这会同时停止播放进度和保存进度的定时器
        audioPlayer?.stop()
        
        // 使用保存的状态信息进行最后一次保存
        if lastSeasonNumber > 0 && lastEpisodeNumber > 0 && !lastAudioName.isEmpty && !lastEpisodeTitle.isEmpty {
            let info: [String: Any] = [
                "seasonNumber": lastSeasonNumber,
                "episodeNumber": lastEpisodeNumber,
                "currentTime": lastCurrentTime,
                "audioName": lastAudioName,
                "episodeTitle": lastEpisodeTitle
            ]
            print("[PlayerViewModel] 保存播放信息:")
            print("- 季数: \(lastSeasonNumber)")
            print("- 集数: \(lastEpisodeNumber)")
            print("- 音频: \(lastAudioName)")
            print("- 标题: \(lastEpisodeTitle)")
            print("- 播放时间: \(lastCurrentTime)")
            UserDefaults.standard.set(info, forKey: lastPlayedKey)
            UserDefaults.standard.synchronize()  // 确保立即写入
        } else {
            print("[PlayerViewModel] ⚠️ 清理时检测到无效的播放信息，跳过保存")
            print("- 季数: \(lastSeasonNumber)")
            print("- 集数: \(lastEpisodeNumber)")
            print("- 音频: \(lastAudioName)")
            print("- 标题: \(lastEpisodeTitle)")
        }
        
        // 然后再清理其他资源和状态
        audioPlayer = nil
        currentTime = 0
        totalTime = 0
        isPlaying = false
        cancelSleepTimer()
        hasLoaded = false  // 重置加载状态
        
        // 清理远程控制管理器
        remoteControlManager?.cleanup()
        remoteControlManager = nil
        
        print("[PlayerViewModel] ===== 清理完成 =====")
    }
    
    deinit {
        print("[PlayerViewModel] ===== 开始 deinit =====")
        cleanup()
        print("[PlayerViewModel] ===== deinit 完成 =====")
    }
    
    // 音频播放结束时的处理
    func handlePlaybackFinished() {
        // print("[PlayMode] 播放结束处理 - 当前模式: \(playMode)")
        
        switch playMode {
        case .sequential:
            // 顺序播放模式：播放下一句
            if let currentSubtitle = getCurrentSubtitle(),
               let currentIndex = subtitles.firstIndex(where: { $0.id == currentSubtitle.id }) {
                // print("[PlayMode] 顺序播放 - 当前字幕索引: \(currentIndex)")
                if currentIndex < subtitles.count - 1 {
                    // 还有下一句，继续播放
                    let nextSubtitle = subtitles[currentIndex + 1]
                    // print("[PlayMode] 顺序播放 - 播放下一句: \(nextSubtitle.english)")
                    if let player = audioPlayer {
                        player.currentTime = nextSubtitle.startTime
                        player.play()
                        remoteControlManager?.updateNowPlayingInfo()
                    }
                } else {
                    // 已是最后一句，停止播放
                    // print("[PlayMode] 顺序播放 - 已到最后一句，停止播放")
                    isPlaying = false
                    stopProgressTimer()
                    remoteControlManager?.updateNowPlayingInfo()
                }
            }
            
        case .singleSentence:
            // 单句循环模式：重新播放当前句子
            if let currentSubtitle = getCurrentSubtitle() {
                // print("[PlayMode] 单句循环 - 重复播放当前句子: \(currentSubtitle.english)")
                // 使用 playSubtitle 而不是直接 seek
                playSubtitle(currentSubtitle)
            } else if let nearestSubtitle = getNearestSubtitle() {
                // 如果获取不到当前字幕，使用最近的字幕
                // print("[PlayMode] 单句循环 - 使用最近字幕: \(nearestSubtitle.english)")
                playSubtitle(nearestSubtitle)
            }
            
        case .singleEpisode:
            // 单集循环模式：回到第一句重新播放
            // print("[PlayMode] 单集循环 - 重新开始播放")
            if let firstSubtitle = subtitles.first {
                playSubtitle(firstSubtitle)
            }
            
        case .abLoop:
            // AB循环模式：回到起点重新播放
            if let startTime = abLoopStartTime {
                // print("[PlayMode] AB循环 - 返回A点: \(startTime)")
                seek(to: startTime)
                if let player = audioPlayer {
                    player.play()
                    isPlaying = true
                    startProgressTimer()
                }
            }
        }
    }
    
    // 设置 AB 循环的起止点
    func setABLoopPoint(_ subtitle: Subtitle) {
        // print("[ABLoop] 开始设置循环点")
        // print("[ABLoop] 当前状态: \(abLoopState)")
        // print("[ABLoop] 是否启用AB点设置: \(isABLoopSettingEnabled)")
        
        // 如果没有启用AB点设置，直接返回
        guard isABLoopSettingEnabled else {
            // print("[ABLoop] AB点设置未启用，忽略点击")
            return
        }
        
        switch abLoopState {
        case .settingA:
            // 设置 A 点
            // print("[ABLoop] 设置 A 点: \(subtitle.startTime)")
            // print("[ABLoop] A 点字幕内容: \(subtitle.english)")
            abLoopStartTime = subtitle.startTime
            abLoopEndTime = nil
            abLoopState = .settingB
            
        case .settingB:
            // 设置 B 点
            if let startTime = abLoopStartTime,
               subtitle.startTime > startTime {
                // print("[ABLoop] 设置 B 点: \(subtitle.endTime)")
                // print("[ABLoop] B 点字幕内容: \(subtitle.english)")
                abLoopEndTime = subtitle.endTime
                abLoopState = .looping
                isABLoopSettingEnabled = false  // 设置完成后禁用设置
                
                // 从 A 点开始播放
                // print("[ABLoop] 开始从 A 点播放循环")
                seek(to: startTime)
                if let player = audioPlayer {
                    player.play()
                    isPlaying = true
                }
            } else {
                // print("[ABLoop] B 点设置失败：B 点必须在 A 点之后")
                // print("[ABLoop] A 点时间: \(String(describing: abLoopStartTime))")
                // print("[ABLoop] 尝试设置的 B 点时间: \(subtitle.startTime)")
            }
            
        default:
            // print("[ABLoop] 当前状态不支持设置循环点: \(abLoopState)")
            break
        }
    }
    
    // 添加一个方法来检查是否可以播放字幕
    func canPlaySubtitle() -> Bool {
        // 如果是 AB 循环模式且还没设置完 AB 点，则不允许播放
        if playMode == .abLoop && abLoopState != .looping {
            return false
        }
        return true
    }
    
    // 用于存储上次播信息的 key
    private let lastPlayedKey = "LastPlayedInfo"
    private let firstLaunchKey = "IsFirstLaunch"
    
    // 存储上次播放位置
    private var lastSaveTime: Date?
    private let minimumSaveInterval: TimeInterval = 30 // 30秒
    
    private func saveLastPlayedInfo() {
        // 如果还没有加载完成，跳过保存
        guard hasLoaded else {
            print("[PlayerViewModel] ⚠️ 播放器未完成加载，跳过保存")
            return
        }
        
        print("[PlayerViewModel] 开始保存播放进度")
        print("- 季数: \(currentSeasonNumber)")
        print("- 集数: \(currentEpisodeNumber)")
        print("- 音频: \(currentAudioName)")
        print("- 标题: \(currentEpisodeTitle)")
        print("- 当前时间: \(currentTime)")
        
        // 检查当前状态是否有效
        if currentSeasonNumber > 0 && 
           currentEpisodeNumber > 0 && 
           !currentAudioName.isEmpty && 
           !currentEpisodeTitle.isEmpty {
            // 创建要保存的信息
            let info: [String: Any] = [
                "seasonNumber": currentSeasonNumber,
                "episodeNumber": currentEpisodeNumber,
                "currentTime": currentTime,
                "audioName": currentAudioName,
                "episodeTitle": currentEpisodeTitle
            ]
            
            // 检查是否与上次保存的信息相同
            if let lastInfo = UserDefaults.standard.dictionary(forKey: lastPlayedKey),
               let lastTime = lastInfo["currentTime"] as? TimeInterval,
               lastInfo["seasonNumber"] as? Int == info["seasonNumber"] as? Int,
               lastInfo["episodeNumber"] as? Int == info["episodeNumber"] as? Int,
               lastInfo["audioName"] as? String == info["audioName"] as? String,
               abs(lastTime - currentTime) < 1.0 {  // 如果时间差小于1秒，跳过保存
                print("[PlayerViewModel] ℹ️ 与上次保存信息相同，跳过保存")
                return
            }
            
            UserDefaults.standard.set(info, forKey: lastPlayedKey)
            UserDefaults.standard.synchronize()
            print("[PlayerViewModel] ✅ 播放进度保存成功")
        } else {
            print("[PlayerViewModel] ⚠️ 播放信息不完整，跳过保存")
        }
    }
    
    // 读取上次播放位置
    func loadLastPlayedInfo() -> [String: Any]? {
        print("[PlayerViewModel] 尝试加载上次播放信息")
        if let info = UserDefaults.standard.dictionary(forKey: lastPlayedKey) {
            print("[PlayerViewModel] 找到上次播放记录:")
            print("- 季数: \(info["seasonNumber"] ?? "nil")")
            print("- 集数: \(info["episodeNumber"] ?? "nil")")
            print("- 音频: \(info["audioName"] ?? "nil")")
            print("- 标题: \(info["episodeTitle"] ?? "nil")")
            print("- 播放时间: \(info["currentTime"] ?? "nil")")
            return info
        } else {
            print("[PlayerViewModel] ⚠️ 未找到上次播放记录")
            return nil
        }
    }
    
    // 检查是否是首次启动
    func isFirstLaunch() -> Bool {
        let isFirst = !UserDefaults.standard.bool(forKey: firstLaunchKey)
        // print("===== 首次启动检查 =====")
        // print("firstLaunchKey: \(firstLaunchKey)")
        // print("当前值: \(!isFirst)")
        // print("是否首次启动: \(isFirst)")
        if isFirst {
            // print("设置首次启动标记")
            UserDefaults.standard.set(true, forKey: firstLaunchKey)
        }
        // print("======================")
        return isFirst
    }
    
    // 修改 remoteControlManager 的访问级别
    var remoteControlManager: RemoteControlManager?
    
    // 添加初始化远程控制的方法
    func initializeRemoteControl() {
        if remoteControlManager == nil {
            remoteControlManager = RemoteControlManager(playerViewModel: self)
        }
    }
    
    @Published var isExporting = false
    
    @Published var sleepTimer: Timer?
    @Published var remainingSleepTime: TimeInterval = 0
    
    func setSleepTimer(_ duration: TimeInterval) {
        // 取消现有的定时器
        cancelSleepTimer()
        
        // print("[SleepTimer] 设置定时关闭: \(duration) 秒")
        
        // 设置新的定时器
        remainingSleepTime = duration
        sleepTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            if self.remainingSleepTime > 0 {
                self.remainingSleepTime -= 1
                // 每分钟打印一次剩余时间
                if Int(self.remainingSleepTime) % 60 == 0 {
                    // print("[SleepTimer] 剩余时间: \(Int(self.remainingSleepTime)/60) 分钟")
                }
            } else {
                // print("[SleepTimer] ===== 定时器时间到 =====")
                // print("[SleepTimer] 当前播放状态: \(self.isPlaying ? "播放中" : "已暂停")")
                
                // 时间到，停止播放
                if self.isPlaying {
                    // print("[SleepTimer] 执行自动停止播放")
                    self.togglePlayPause()
                    // print("[SleepTimer] 播放已停止")
                }
                
                // print("[SleepTimer] 清理定时器")
                self.cancelSleepTimer()
                // print("[SleepTimer] =====================")
            }
        }
        
        // print("[SleepTimer] 定时器已启动，将在 \(Int(duration)/60) 分钟后停止播放")
    }
    
    func cancelSleepTimer() {
        if sleepTimer != nil {
            // print("[SleepTimer] 取消定时器")
            // print("[SleepTimer] - 剩余时间: \(Int(remainingSleepTime)/60) 分钟")
        }
        
        sleepTimer?.invalidate()
        sleepTimer = nil
        remainingSleepTime = 0
        
        if sleepTimer == nil {
            // print("[SleepTimer] 定时器已清理完成")
        }
    }
    
    // 添加 AB 循环状态指示器
    @Published var abLoopState: ABLoopState = .notSet {
        didSet {
            // 当状态改变时更新提示信息
            switch abLoopState {
            case .notSet:
                abLoopPrompt = nil
            case .settingA:
                abLoopPrompt = "请选择循环起点 (A点)"
            case .settingB:
                abLoopPrompt = "请选择循环终点 (B点)"
            case .looping:
                abLoopPrompt = nil
            }
        }
    }
    
    @Published var abLoopPrompt: String? = nil
    
    // 重置 AB 循环
    func resetABLoop() {
        abLoopStartTime = nil
        abLoopEndTime = nil
        abLoopState = .notSet
    }
    
    // 修改资源文件完整性检查方法
    func areResourceFilesComplete() -> Bool {
        // 第一季第一集直接返回 true，因为资源文件已打包在 Bundle 中
        if currentSeasonNumber == 1 && currentEpisodeNumber == 1 {
            return true
        }
        
        guard !currentAudioName.isEmpty else { return false }
        
        return downloadViewModel.areResourceFilesComplete(
            audioName: currentAudioName,
            subtitleName: currentAudioName
        )
    }
    
    // 修改重新下载方法
    func redownloadCurrentEpisode() {
        guard !currentAudioName.isEmpty else { return }
        
        downloadViewModel.redownload(
            audioName: currentAudioName,
            subtitleName: currentAudioName,
            seasonNumber: currentSeasonNumber,
            episodeNumber: currentEpisodeNumber,
            episodeTitle: currentEpisodeTitle
        ) { [weak self] in
            self?.loadFiles(
                audioName: self?.currentAudioName ?? "",
                subtitleName: self?.currentAudioName ?? "",
                seasonNumber: self?.currentSeasonNumber ?? 1,
                episodeNumber: self?.currentEpisodeNumber ?? 1,
                episodeTitle: self?.currentEpisodeTitle ?? ""
            )
        }
    }
    
    // 添加一个新的状态来控制是否允许设置AB点
    @Published var isABLoopSettingEnabled = false
    
    // 在 PlayerViewModel 中添加方法来安全地修改 AB 循环状态
    func setupABLoop(state: ABLoopState) {
        abLoopState = state
        isABLoopSettingEnabled = true
        
        switch state {
        case .settingA:
            abLoopStartTime = nil
            abLoopEndTime = nil
        case .settingB:
            abLoopEndTime = nil
        default:
            break
        }
    }
    
    // 在 PlayerViewModel 类中添加
    func pausePlaybackAndResetABLoop() {
        // 重置 AB 循环状态
        resetABLoop()
        // 设置完整的初始状态
        abLoopState = .settingA
        isABLoopSettingEnabled = true
        // 暂停当前播放
        if isPlaying {
            isPlaying = false
            audioPlayer?.pause()
        }
        // 停止任何现有的循环
        audioPlayer?.stopLoopTimer()
    }
    
    // 添加属性来记录用户想要观看的剧集
    var pendingEpisode: (audioName: String, subtitleName: String, seasonNumber: Int, episodeNumber: Int, episodeTitle: String)?

    // 3. App进入后台时保存
    func sceneDidEnterBackground() {
        saveLastPlayedInfo()
    }
    
    // 添加 hasLoaded 属性
    @Published var hasLoaded = false  // 改为公开属性
    
    // 添加 DownloadViewModel 属性
    private var downloadViewModel: DownloadViewModel {
        DownloadViewModel.shared
    }
    
    // 新增获取最近字幕的方法
    func getNearestSubtitle() -> Subtitle? {
        guard !subtitles.isEmpty else { return nil }
        
        // 找到第一个结束时间大于当前时间的字幕
        if let nextSubtitle = subtitles.first(where: { $0.endTime > currentTime }) {
            // 如果找到的是第一个字幕,直接返回
            if nextSubtitle.index == subtitles.first?.index {
                return nextSubtitle
            }
            
            // 找到前一个字幕
            if let currentIndex = subtitles.firstIndex(where: { $0.id == nextSubtitle.id }),
               currentIndex > 0 {
                let previousSubtitle = subtitles[currentIndex - 1]
                
                // 计算与前后字幕的时间差
                let distanceToPrevious = abs(currentTime - previousSubtitle.startTime)
                let distanceToNext = abs(currentTime - nextSubtitle.startTime)
                
                // 返回距离当前时间最近的字幕
                return distanceToPrevious < distanceToNext ? previousSubtitle : nextSubtitle
            }
            
            return nextSubtitle
        }
        
        // 如果所有字幕都在当前时间之前,返回最后一个字幕
        return subtitles.last
    }
    
    // 添加音频中断相关的属性
    private var wasPlayingBeforeInterruption = false
    private var interruptedTime: TimeInterval = 0
    
    // 处理音频中断开始
    func handleAudioInterruptionBegan() {
        // print("[PlayerViewModel] 音频播放被打断")
        // 保存当前播放状态
        wasPlayingBeforeInterruption = isPlaying
        if let player = audioPlayer {
            interruptedTime = player.currentTime
        }
        
        // 暂停播放
        if isPlaying {
            isPlaying = false
            audioPlayer?.pause()
        }
    }
    
    // 处理音频中断结束
    func handleAudioInterruptionEnded() {
        // print("[PlayerViewModel] 音频中断结束，wasPlaying: \(wasPlayingBeforeInterruption)")
        if wasPlayingBeforeInterruption {
            // 恢复到中断时的位置
            audioPlayer?.currentTime = interruptedTime
            // 恢复播放
            isPlaying = true
            audioPlayer?.play()
            // 重新开始进度更新定时器
            startProgressTimer()
        }

        // 重置状态
        wasPlayingBeforeInterruption = false
        interruptedTime = 0
    }

    // 强制重新加载当前剧集的字幕（用于调试和更新字幕文件后的重新加载）
    func reloadCurrentSubtitles() {
        print("[PlayerViewModel] 🔄 强制重新加载当前剧集字幕")
        print("- 当前季数: \(currentSeasonNumber)")
        print("- 当前集数: \(currentEpisodeNumber)")
        print("- 当前音频: \(currentAudioName)")
        print("- 当前标题: \(currentEpisodeTitle)")

        // 清除当前字幕缓存
        subtitles.removeAll()
        currentSubtitle = nil
        print("[PlayerViewModel] 🗑️ 已清除字幕缓存")

        // 获取当前剧集的字幕文件名
        guard let show = ShowManager.shared.loadShow(),
              let season = show.seasons.first(where: { $0.number == currentSeasonNumber }),
              let episode = season.episodes.first(where: { $0.number == currentEpisodeNumber }) else {
            print("[PlayerViewModel] ❌ 无法找到当前剧集信息")
            return
        }

        let subtitleName = episode.subtitleName
        print("[PlayerViewModel] 字幕文件名: \(subtitleName)")

        // 重新加载字幕
        reloadSubtitles(subtitleName: subtitleName, seasonNumber: currentSeasonNumber, episodeNumber: currentEpisodeNumber)
    }

    // 重新加载字幕的具体实现
    private func reloadSubtitles(subtitleName: String, seasonNumber: Int, episodeNumber: Int) {
        var subtitleURL: URL?

        // 特殊处理第一季第一集
        if seasonNumber == 1 && episodeNumber == 1 {
            // 尝试从 Bundle 的 Subtitles 目录加载
            if let bundleURL = Bundle.main.url(forResource: "Subtitles/S01E01", withExtension: "srt") {
                subtitleURL = bundleURL
                print("[字幕重新加载] 从 Bundle Subtitles 目录加载字幕: \(bundleURL)")
            } else if let bundleURL = Bundle.main.url(forResource: "S01E01", withExtension: "srt") {
                subtitleURL = bundleURL
                print("[字幕重新加载] 从 Bundle 根目录加载字幕: \(bundleURL)")
            } else {
                print("[字幕重新加载] 未在 Bundle 中找到字幕文件，尝试从 Documents 目录加载")
                subtitleURL = DownloadManager.shared.getLocalFileURL(for: "\(subtitleName).srt")
                if let docURL = subtitleURL {
                    print("[字幕重新加载] 从 Documents 目录加载字幕: \(docURL)")
                } else {
                    print("[字幕重新加载] Documents 目录中也未找到字幕文件")
                }
            }
        } else {
            // 其他剧集使用原有逻辑
            subtitleURL = DownloadManager.shared.getLocalFileURL(for: "\(subtitleName).srt")
        }

        if let subtitleURL = subtitleURL {
            print("[字幕重新加载] 🔄 开始重新加载字幕文件: \(subtitleURL.path)")
            do {
                let content = try String(contentsOf: subtitleURL, encoding: .utf8)
                print("[字幕重新加载] 文件内容长度: \(content.count) 字符")
                print("[字幕重新加载] 文件内容前200字符: \(String(content.prefix(200)))")
                subtitles = SubtitleParser.parseSubtitles(from: content)
                print("[字幕重新加载] 成功解析字幕数量: \(subtitles.count)")

                // 打印前几个字幕用于调试
                if subtitles.count > 0 {
                    print("[字幕重新加载] 第一个字幕: \(subtitles[0].english) | \(subtitles[0].chinese)")
                    print("[字幕重新加载] 第一个字幕时间: \(subtitles[0].startTime) - \(subtitles[0].endTime) 秒")
                }
                if subtitles.count > 1 {
                    print("[字幕重新加载] 第二个字幕: \(subtitles[1].english) | \(subtitles[1].chinese)")
                    print("[字幕重新加载] 第二个字幕时间: \(subtitles[1].startTime) - \(subtitles[1].endTime) 秒")
                }
                if subtitles.count > 2 {
                    print("[字幕重新加载] 第三个字幕: \(subtitles[2].english) | \(subtitles[2].chinese)")
                    print("[字幕重新加载] 第三个字幕时间: \(subtitles[2].startTime) - \(subtitles[2].endTime) 秒")
                }

                // 更新当前字幕
                updateCurrentSubtitle()

                // 触发滚动到当前字幕
                shouldScrollToSubtitle = true

                print("[字幕重新加载] ✅ 重新加载完成，当前字幕已更新")
            } catch {
                print("[字幕重新加载] ❌ 加载失败: \(error)")
            }
        } else {
            print("[字幕重新加载] ❌ 未找到字幕文件: \(subtitleName).srt")
        }
    }
}

// MARK: - Episode Loading
extension PlayerViewModel {
    func handleEpisodeLoading() {
        print("[PlayerViewModel] 🚀 开始处理剧集加载")

        // 加载剧集数据
        guard let show = ShowManager.shared.loadShow() else {
            print("[PlayerViewModel] ❌ 加载剧集数据失败")
            return
        }
        print("[PlayerViewModel] ✅ 成功加载剧集数据")

        // 检查是否是首次启动
        if isFirstLaunch() {
            print("[PlayerViewModel] 🆕 首次启动：准备播放第一集")
            loadFirstEpisodeDirectly(show: show)
            return
        }

        // 非首次启动：尝试加载上次播放位置
        print("[PlayerViewModel] 🔄 非首次启动：尝试加载上次播放位置")
        if let lastPlayed = loadLastPlayedInfo(),
           let seasonNumber = lastPlayed["seasonNumber"] as? Int,
           let episodeNumber = lastPlayed["episodeNumber"] as? Int,
           let currentTime = lastPlayed["currentTime"] as? TimeInterval,
           let audioName = lastPlayed["audioName"] as? String,
           let episodeTitle = lastPlayed["episodeTitle"] as? String {

            print("[PlayerViewModel] 📋 解析上次播放信息:")
            print("- seasonNumber: \(seasonNumber)")
            print("- episodeNumber: \(episodeNumber)")
            print("- currentTime: \(currentTime)")
            print("- audioName: \(audioName)")
            print("- episodeTitle: \(episodeTitle)")

            // 查找对应的字幕文件名
            if let subtitleName = show.seasons
                .first(where: { $0.number == seasonNumber })?
                .episodes
                .first(where: { $0.number == episodeNumber })?
                .subtitleName {

                // 应用启动时，无论是否是付费剧集，都直接加载，不检查会员权限
                // 这样可以避免启动时显示购买页面
                print("[PlayerViewModel] 🚀 应用启动时直接加载剧集，跳过权限检查")
                loadEpisodeDirectly(
                    audioName: audioName,
                    subtitleName: subtitleName,
                    seasonNumber: seasonNumber,
                    episodeNumber: episodeNumber,
                    episodeTitle: episodeTitle
                )
                // 定位到上次播放位置
                seek(to: currentTime)
            } else {
                print("[PlayerViewModel] ⚠️ 无法找到对应的字幕文件，加载第一集")
                loadFirstEpisodeDirectly(show: show)
            }
        } else {
            print("[PlayerViewModel] 📝 未找到有效的上次播放记录，加载第一集")
            loadFirstEpisodeDirectly(show: show)
        }
    }
    
    private func loadFirstEpisode(show: Show) {
        if let firstSeason = show.seasons.first,
           let firstEpisode = firstSeason.episodes.first {
            // print("[PlayerViewModel] 加载第一集: S\(firstSeason.number)E\(firstEpisode.number)")
            loadEpisode(
                audioName: firstEpisode.audioName,
                subtitleName: firstEpisode.subtitleName,
                seasonNumber: firstSeason.number,
                episodeNumber: firstEpisode.number,
                episodeTitle: firstEpisode.title
            )
        } else {
            // print("[PlayerViewModel] ⚠️ 无法获取第一集信息")
        }
    }

    // 直接加载第一集，不检查会员权限（用于应用启动）
    private func loadFirstEpisodeDirectly(show: Show) {
        if let firstSeason = show.seasons.first,
           let firstEpisode = firstSeason.episodes.first {
            print("[PlayerViewModel] 🎬 直接加载第一集: S\(firstSeason.number)E\(firstEpisode.number)")
            print("[PlayerViewModel] 📁 音频文件: \(firstEpisode.audioName)")
            print("[PlayerViewModel] 📝 字幕文件: \(firstEpisode.subtitleName)")

            loadEpisodeDirectly(
                audioName: firstEpisode.audioName,
                subtitleName: firstEpisode.subtitleName,
                seasonNumber: firstSeason.number,
                episodeNumber: firstEpisode.number,
                episodeTitle: firstEpisode.title
            )

            print("[PlayerViewModel] ✅ 第一集直接加载完成")
        } else {
            print("[PlayerViewModel] ❌ 无法获取第一集信息")
        }
    }

    // 直接加载剧集，跳过会员权限检查（用于免费剧集或应用启动）
    private func loadEpisodeDirectly(
        audioName: String,
        subtitleName: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String
    ) {
        print("[PlayerViewModel] 🎬 直接加载剧集: S\(seasonNumber)E\(episodeNumber)")

        // 更新当前剧集信息
        currentSeasonNumber = seasonNumber
        currentEpisodeNumber = episodeNumber
        currentEpisodeTitle = episodeTitle
        currentAudioName = audioName

        // 直接加载文件，跳过权限检查，优先从本地加载
        Task {
            await MainActor.run {
                // 优先尝试从本地加载文件（包括Bundle和Documents目录）
                print("[PlayerViewModel] 🔍 优先尝试从本地加载文件")

                // 直接尝试加载本地文件
                loadFiles(
                    audioName: audioName,
                    subtitleName: subtitleName,
                    seasonNumber: seasonNumber,
                    episodeNumber: episodeNumber,
                    episodeTitle: episodeTitle
                )

                // 检查加载是否成功（通过检查是否有音频播放器）
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    guard let self = self else { return }

                    // 如果本地加载失败，再考虑下载
                    if self.audioPlayer == nil {
                        print("[PlayerViewModel] ⚠️ 本地文件加载失败，开始云端下载")

                        self.downloadViewModel.startDownload(
                            audioName: audioName,
                            subtitleName: subtitleName,
                            seasonNumber: seasonNumber,
                            episodeNumber: episodeNumber,
                            episodeTitle: episodeTitle
                        ) { [weak self] in
                            self?.loadFiles(
                                audioName: audioName,
                                subtitleName: subtitleName,
                                seasonNumber: seasonNumber,
                                episodeNumber: episodeNumber,
                                episodeTitle: episodeTitle
                            )
                        }
                    } else {
                        print("[PlayerViewModel] ✅ 本地文件加载成功")
                    }
                }

                remoteControlManager?.updateNowPlayingInfo()
            }
        }
    }
}
