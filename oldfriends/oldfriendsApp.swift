//
//  DramaLingoApp.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI
import BackgroundTasks
import StoreHelper
import Network
import AppTrackingTransparency
import FirebaseCore
import os.log

// 添加自定义环境键
private struct StoreReadyKey: EnvironmentKey {
    static let defaultValue: Bool = false
}

// 扩展 EnvironmentValues 添加 isStoreReady 属性
extension EnvironmentValues {
    var isStoreReady: Bool {
        get { self[StoreReadyKey.self] }
        set { self[StoreReadyKey.self] = newValue }
    }
}

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication,
                    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        FirebaseApp.configure()
        return true
    }
}

@main
struct DramaLingoApp: App {
    // register app delegate for Firebase setup
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate
    
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "DramaLingoApp")
    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var viewModel = PlayerViewModel()
    @StateObject var storeHelper = StoreHelper()
    @State private var showLaunchScreen = true
    @State private var isStoreReady = false
    @State private var showNetworkAlert = false
    private let monitor = NWPathMonitor()
    
    init() {
        // 在 App 初始化时注册后台任务
        DownloadManager.registerBackgroundTasks()
        
        // 记录初始主题设置
        let savedTheme = UserDefaults.standard.string(forKey: "app_theme")
        let isManuallySet = UserDefaults.standard.bool(forKey: "theme_manually_set")
        logger.info("应用启动时的主题设置 - 保存的主题: \(savedTheme ?? "nil"), 是否手动设置: \(isManuallySet)")
    }
    
    var body: some Scene {
        WindowGroup {
            ZStack {
                ContentView()
                    .environmentObject(viewModel)
                    .environmentObject(storeHelper)
                    .environment(\.isStoreReady, isStoreReady)
                    .onAppear {
                        // 初始化主题
                        if let savedTheme = UserDefaults.standard.string(forKey: "app_theme") {
                            let isManuallySet = UserDefaults.standard.bool(forKey: "theme_manually_set")
                            logger.info("初始化主题 - 已保存的主题: \(savedTheme), 是否手动设置: \(isManuallySet)")
                            viewModel.currentTheme = savedTheme == "dark" ? .dark : .light
                            viewModel.isThemeManuallySet = isManuallySet
                        } else {
                            logger.info("初始化主题 - 使用系统主题: \(colorScheme == .dark ? "深色" : "浅色")")
                            viewModel.currentTheme = colorScheme == .dark ? .dark : .light
                            viewModel.isThemeManuallySet = false
                        }
                    }
                    .onChange(of: colorScheme) { newValue in
                        logger.info("系统主题变化: \(colorScheme == .dark ? "深色" : "浅色") -> \(newValue == .dark ? "深色" : "浅色")")
                        if !viewModel.isThemeManuallySet {
                            logger.info("跟随系统主题变化")
                            viewModel.currentTheme = newValue == .dark ? .dark : .light
                        } else {
                            logger.info("已手动设置主题，忽略系统主题变化")
                        }
                    }
                    .task {
                        // 先检查网络权限
                        await checkNetworkPermissionAndStartStore()
                    }
                    .alert("需要网络访问权限", isPresented: $showNetworkAlert) {
                        Button("去设置") {
                            if let url = URL(string: UIApplication.openSettingsURLString) {
                                UIApplication.shared.open(url)
                            }
                        }
                        Button("取消", role: .cancel) { }
                    } message: {
                        Text("听商务英语 需要网络访问权限来获取内容和处理购买。请在设置中允许网络访问。")
                    }
                
                if showLaunchScreen {
                    LaunchScreenView(showLaunchScreen: $showLaunchScreen)
                        .transition(.opacity)
                        .zIndex(1)
                }
            }
        }
        .onChange(of: scenePhase) { newPhase in
            if newPhase == .background {
                viewModel.sceneDidEnterBackground()
            } else if newPhase == .active {
                // 当应用回到前台时，重新检查网络状态
                Task {
                    await checkNetworkPermissionAndStartStore()
                }
            }
        }
    }
    
    @MainActor
    private func checkNetworkPermissionAndStartStore() async {
        await withCheckedContinuation { continuation in
            var hasResumed = false

            let task = Task { @MainActor in
                monitor.pathUpdateHandler = { path in
                    Task { @MainActor in
                        if !hasResumed {
                            hasResumed = true

                            // 检查网络权限状态
                            if path.status == .requiresConnection {
                                // 用户未授权网络访问，但仍然尝试启动 StoreHelper
                                logger.warning("网络权限受限，但仍尝试启动 StoreHelper")
                                showNetworkAlert = true

                                // 即使网络受限也启动 StoreHelper，因为可能有缓存的购买信息
                                storeHelper.start()
                                MembershipManager.shared.setStoreHelper(storeHelper)
                                isStoreReady = true
                            } else {
                                // 用户已授权，启动 StoreHelper
                                storeHelper.start()
                                MembershipManager.shared.setStoreHelper(storeHelper)
                                await MembershipManager.shared.checkAndRestorePurchases()
                                isStoreReady = true
                                logger.info("网络已授权，StoreHelper 初始化成功")
                            }

                            continuation.resume()

                            // 延迟取消监控，给一些时间让网络状态稳定
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                monitor.cancel()
                                monitor.pathUpdateHandler = nil
                            }
                        }
                    }
                }

                monitor.start(queue: DispatchQueue.main)
            }
            
            Task {
                do {
                    try await task.value
                } catch {
                    if !hasResumed {
                        hasResumed = true
                        continuation.resume()
                    }
                }
            }
        }
    }
}

