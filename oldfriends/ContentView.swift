//
//  ContentView.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//
//  能力边界:
//  1. 作为应用的根视图容器，负责整体布局和导航结构
//  2. 管理全局状态和环境对象:
//     - WordSelectionManager: 单词选择管理
//     - FavoriteManager: 收藏功能管理
//     - PlayerViewModel: 播放器核心逻辑
//     - StoreHelper: 应用内购买
//  3. 处理主题切换和持久化
//  4. 提供全局 Toast 消息展示
//  5. 控制剧集选择面板的显示
//
//  代码位置:
//  - 路径: DramaLingo/ContentView.swift
//  - 层级: 根视图层
//  - 依赖视图:
//    ├── PlayerView (主要内容视图)
//    ├── LoadingView (加载提示)
//    ├── ToastView (消息提示)
//    └── ShowDrawerView (剧集选择面板)
//

import SwiftUI
import AVFoundation
import UIKit
import Foundation
import MediaPlayer
import StoreHelper
import StoreKit

struct ContentView: View {
    @StateObject private var wordSelectionManager = WordSelectionManager()
    @StateObject private var favoriteManager = FavoriteManager()
    @StateObject private var viewModel = PlayerViewModel()
    @EnvironmentObject private var storeHelper: StoreHelper
    @Environment(\.colorScheme) var colorScheme
    @State private var toastMessage: String? = nil
    @State private var showEpisodeSheet = false
    @State private var showPurchaseView = false
    @State private var hasInitialized = false
    @State private var hasLoadedEpisode = false
    @StateObject private var membershipManager = MembershipManager.shared
    
    var body: some View {
        Group {
            if hasInitialized {
                NavigationView {
                    ZStack {
                        PlayerView()
                            .environmentObject(wordSelectionManager)
                            .environmentObject(favoriteManager)
                            .environmentObject(viewModel)
                            .tint(ThemeColors.primary)
                            .navigationBarHidden(true)
                        
                        if viewModel.isExporting {
                            LoadingView(message: "正在生成 PDF...")
                        }
                        
                        if let message = toastMessage {
                            ToastView(message: message)
                        }
                    }
                }
                .navigationViewStyle(.stack)
                .environmentObject(favoriteManager)
                .environmentObject(viewModel)
            } else {
                Color.clear.onAppear {
                    // 初始化主题
                    if let savedTheme = UserDefaults.standard.string(forKey: "app_theme") {
                        viewModel.currentTheme = savedTheme == "dark" ? .dark : .light
                        viewModel.isThemeManuallySet = UserDefaults.standard.bool(forKey: "theme_manually_set")
                    } else {
                        viewModel.currentTheme = colorScheme == .dark ? .dark : .light
                        viewModel.isThemeManuallySet = false
                    }
                    
                    // 初始化 StoreHelper
                    viewModel.setStoreHelper(storeHelper)
                    hasInitialized = true
                }
            }
        }
        .onAppear {
            // 只在首次加载时调用
            if !hasLoadedEpisode {
                viewModel.handleEpisodeLoading()
                hasLoadedEpisode = true
            }
        }
        .onChange(of: colorScheme) { newValue in
            // 只有在用户没有手动设置主题时，才跟随系统主题
            if !viewModel.isThemeManuallySet {
                viewModel.currentTheme = newValue == .dark ? .dark : .light
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowSuccessToast"))) { notification in
            if let message = notification.userInfo?["message"] as? String {
                withAnimation {
                    toastMessage = message
                }
                // 3秒后自动隐藏
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    withAnimation {
                        toastMessage = nil
                    }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowEpisodeSelection"))) { _ in
            showEpisodeSheet = true
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowErrorToast"))) { notification in
            if let message = notification.userInfo?["message"] as? String {
                withAnimation {
                    toastMessage = message
                }
                // 3秒后自动隐藏
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    withAnimation {
                        toastMessage = nil
                    }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShowPurchaseView"))) { _ in
            // 收到显示购买页面的通知
            showPurchaseView = true
        }
        .onChange(of: membershipManager.showPurchaseAlert) { newValue in
            if newValue {
                // 确保状态正确
                if !membershipManager.isFromShowDrawer {
                    membershipManager.isFromShowDrawer = false
                }
                showPurchaseView = true
                membershipManager.showPurchaseAlert = false
            }
        }
        .sheet(isPresented: $showEpisodeSheet) {
            ShowDrawerView(theme: viewModel.currentTheme)
                .presentationDetents([.large])
        }
        .sheet(isPresented: $showPurchaseView) {
            PurchaseView(theme: viewModel.currentTheme)
                .environmentObject(storeHelper)
                .onDisappear {
                    // 重置状态
                    membershipManager.showPurchaseAlert = false
                    membershipManager.isFromShowDrawer = false
                }
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}


