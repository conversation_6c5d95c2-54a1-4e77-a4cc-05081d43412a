# SwiftUI @StateObject 使用注意事项

## 问题描述
在 `DramaLingoApp.swift` 中出现错误：
> Accessing StateObject's object without being installed on a View. This will create a new instance each time.

## 原因分析
1. `@StateObject` 是 SwiftUI 的状态管理属性包装器
2. 在视图完全初始化前（如 `init()` 中）访问 `@StateObject` 会导致：
   - 创建额外的实例
   - 状态管理混乱
   - 对象生命周期不正确

## 解决方案
1. 避免在 `init()` 中访问 `@StateObject` 对象
2. 将初始化逻辑移至视图生命周期的适当位置：
   ```swift
   .onAppear {
       // 初始化逻辑
   }
   ```
3. 对于需要启动的服务，使用 `.task` 修饰符：
   ```swift
   .task {
       // 启动服务
   }
   ```

## 最佳实践
1. `@StateObject` 只在视图完全加载后访问
2. 使用适当的生命周期修饰符：
   - `.onAppear`: 用于一次性设置
   - `.task`: 用于异步操作
3. 初始化时的必要设置考虑使用其他方式：
   - 普通属性
   - 单例模式
   - 依赖注入

## 注意事项
- 确保状态对象的生命周期与视图生命周期同步
- 避免在视图初始化前访问状态对象
- 合理规划状态管理的时机
