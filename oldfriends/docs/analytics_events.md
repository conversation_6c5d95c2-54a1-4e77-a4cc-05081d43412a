# DramaLingo Firebase Analytics 埋点方案

## 1. 页面访问埋点

| 页面名称 | 事件名称 | 参数 | 说明 | 代码位置 |
|---------|---------|------|------|----------|
| 播放器主页面 | screen_view | {screen_name: "player"} | 用户访问应用主页面（播放器页面） | PlayerView.swift:83 |
| 剧集选择页面 | screen_view | {screen_name: "show_list"} | 用户访问剧集选择页面 | ShowDrawerView.swift |
| 收藏单词页面 | screen_view | {screen_name: "favorite_words"} | 用户访问收藏单词页面 | FavoriteWordsView.swift |
| 收藏句子页面 | screen_view | {screen_name: "favorite_sentences"} | 用户访问收藏句子页面 | FavoriteWordsView.swift |
| 购买提醒页面 | screen_view | {screen_name: "purchase_alert"} | 用户访问购买提醒页面 | PurchaseAlertView.swift:377 |

## 2. 用户交互埋点

### 2.1 播放器页面相关

| 功能点 | 事件名称 | 参数 | 说明 | 代码位置 |
|-------|---------|------|------|----------|
| 点击升级提示 | upgrade_toast_click | {} | 用户点击播放器页面底部的升级提示 | PlayerView.swift:70 |
| 关闭升级提示 | upgrade_toast_close_click | {} | 用户关闭播放器页面底部的升级提示 | PlayerView.swift:75 |
| 导出PDF | export_pdf_click | {} | 用户点击导出当前剧集字幕为PDF | PlayerTopBarView.swift:72 |

### 2.2 剧集选择页面

| 功能点 | 事件名称 | 参数 | 说明 | 代码位置 |
|-------|---------|------|------|----------|
| 点击关闭按钮 | show_list_close_click | {} | 用户点击关闭剧集选择页面 | ShowDrawerView.swift:40 |
| 点击季数按钮 | season_button_click | {} | 用户点击切换季数按钮 | ShowDrawerView.swift:70 |
| 点击剧集项目 | episode_item_click | {} | 用户点击选择某个剧集 | ShowDrawerView.swift:90 |
| 点击会员状态栏 | show_list_membership_status_click | {} | 用户点击顶部会员状态栏 | ShowDrawerView.swift:130 |
| 点击立即解锁按钮 | show_list_unlock_click | {} | 用户点击立即解锁按钮 | ShowDrawerView.swift:290 |

### 2.3 购买页面

| 功能点 | 事件名称 | 参数 | 说明 | 代码位置 |
|-------|---------|------|------|----------|
| 点击购买按钮 | purchase_button_click | {} | 用户点击购买按钮 | PurchaseAlertView.swift:80 |
| 点击恢复购买 | restore_purchase_click | {} | 用户点击恢复购买按钮 | PurchaseAlertView.swift:293 |
| 点击取消购买 | cancel_purchase_click | {} | 用户点击取消购买按钮 | PurchaseAlertView.swift:349 |

### 2.4 收藏页面

| 功能点 | 事件名称 | 参数 | 说明 | 代码位置 |
|-------|---------|------|------|----------|
| 导出收藏单词 | export_favorite_words_click | {} | 用户点击导出收藏的单词列表 | FavoriteWordsView.swift:360 |
| 导出收藏句子 | export_favorite_sentences_click | {} | 用户点击导出收藏的句子列表 | FavoriteWordsView.swift:392 | 