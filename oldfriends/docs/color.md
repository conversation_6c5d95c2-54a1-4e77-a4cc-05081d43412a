# 英语学习 App 配色方案（专业学习版）

## 一、主色系统

### 1. 品牌主色
- Primary: `#2B6CB0` (知性蓝)
  - 象征知识、智慧和专业
  - 用于主要按钮、重要操作区域
  - Hover: `#2C5282`
  - Active: `#2A4365`

### 2. 中性色
- Background: `#F7FAFC` (书页白)
- Surface: `#FFFFFF` (纯白)
- Text Primary: `#1A202C` (墨黑)
- Text Secondary: `#4A5568` (石墨)
- Border: `#E2E8F0` (银灰)

## 二、功能色系统

### 1. 状态色
- Success: `#48BB78` (学习绿)
- Warning: `#ECC94B` (提醒黄)
- Error: `#F56565` (错误红)
- Info: `#4299E1` (提示蓝)

### 2. 学习模块色
- 影像模块: `#BEE3F8` (视频蓝)
- 单词模块: `#C6F6D5` (词汇绿)
- 跟读模块: `#FEEBC8` (语音橙)
- 游戏模块: `#FED7E2` (趣味粉)
- 收藏模块: `#E9D8FD` (收藏紫)

## 三、材质效果

### 1. 卡片阴影
- 轻浮: `0 1px 3px rgba(0, 0, 0, 0.1)`
- 悬浮: `0 4px 6px rgba(0, 0, 0, 0.1)`
- 重浮: `0 10px 15px rgba(0, 0, 0, 0.1)`

### 2. 玻璃拟态效果
- 背景: `rgba(255, 255, 255, 0.8)`
- 模糊: `backdrop-filter: blur(10px)`
- 边框: `1px solid rgba(226, 232, 240, 0.7)`

## 四、应用场景

### 1. 影像播放界面
- 播放器背景: `#1A202C`
- 控制栏: `rgba(26, 32, 44, 0.9)`
- 进度条: `#2B6CB0`
- 字幕文字: `#FFFFFF`

### 2. 单词学习区域
- 卡片背景: `#FFFFFF`
- 音标文字: `#4A5568`
- 释义背景: `#F7FAFC`
- 例句高亮: `#BEE3F8`

### 3. 跟读练习
- 波形图: `#2B6CB0`
- 评分展示: `#48BB78`
- 录音按钮: `#F56565`
- 进度指示: `#ECC94B`

### 4. 游戏界面
- 背景渐变: `linear-gradient(135deg, #F7FAFC 0%, #EBF8FF 100%)`
- 卡片正面: `#FFFFFF`
- 卡片背面: `#BEE3F8`
- 得分显示: `#48BB78`

## 五、交互效果

### 1. 按钮状态
- 默认: `#2B6CB0`
- 悬停: 亮度+5%
- 点击: 亮度-5%
- 禁用: 透明度 40%

### 2. 动画效果
- 转场: 300ms ease-in-out
- 淡入淡出: 200ms ease
- 缩放: 150ms cubic-bezier(0.4, 0, 0.2, 1)

## 六、设计原则

1. **学习氛围**
   - 柔和的色彩搭配
   - 适中的对比度
   - 减少视觉疲劳

2. **专注体验**
   - 弱化干扰元素
   - 突出学习内容
   - 清晰的信息层级

3. **趣味性**
   - 适度的色彩活力
   - 生动的反馈效果
   - 愉悦的视觉体验

## 七、场景应用建议

### 1. 影像学习
- 暗色背景提升沉浸感
- 控件半透明减少干扰
- 进度条醒目易操作

### 2. 单词学习
- 纯白背景提高可读性
- 柔和色彩区分内容
- 重要信息突出显示

### 3. 游戏环节
- 活泼的色彩搭配
- 醒目的状态反馈
- 愉悦的动效设计

## 八、无障碍适配

### 1. 文本可读性
- 字体最小 14px
- 行高至少 1.5
- 标准字重为主

### 2. 色彩对比度
- 文本对比度 > 4.5:1
- 重要信息对比度 > 7:1
- 提供高对比度模式

## 九、深色模式

### 1. 主要颜色
- Background: `#1A202C`
- Surface: `#2D3748`
- Text Primary: `#F7FAFC`
- Text Secondary: `#A0AEC0`

### 2. 功能色调整
- 降低饱和度 15%
- 提高亮度 10%
- 保持识别度