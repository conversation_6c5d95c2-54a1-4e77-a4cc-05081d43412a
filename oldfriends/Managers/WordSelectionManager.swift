import SwiftUI

class WordSelectionManager: ObservableObject {
    @Published var selectedWord: (word: String, frame: CGRect)? = nil
    //  {
    //     didSet {
    //         if let (word, frame) = selectedWord {
    //             print("""
    //            ==================
    //            WordSelectionManager更新:
    //            - 单词: \(word)
    //            - 位置: \(frame)
    //            ==================
    //            """)
    //         }
    //     }
    // }
} 
