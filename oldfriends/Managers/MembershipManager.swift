import SwiftUI
import StoreKit
import StoreHelper
import os

// MARK: - MembershipManager
class MembershipManager: ObservableObject {
    static let shared = MembershipManager()

    // 添加Logger
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "MembershipManager")

    // MARK: - Properties
    @Published var showPurchaseAlert = false
    @Published var isPurchasing = false
    @Published var isLifetimeMember = false
    @Published var isFromShowDrawer = false {
        didSet {
            // print("[MembershipManager] isFromShowDrawer 状态变化: \(oldValue) -> \(isFromShowDrawer)")
        }
    }
    @Published private(set) var isStoreReady = false
    private var storeHelper: StoreHelper?
    private let purchaseStatusKey = "com.dramalingo.purchaseStatus"
    private var restoreRetryCount = 0
    private let maxRetries = 2
    private let retryDelay = 2.0 // 重试延迟时间（秒）

    // MARK: - Initialization
    private init() {
        // 加载购买状态
        loadPurchaseStatus()
    }

    // MARK: - Public Methods
    func setStoreHelper(_ helper: StoreHelper) {
        self.storeHelper = helper
        self.isStoreReady = true

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePurchaseCompletion),
            name: NSNotification.Name("storekit.purchase.completed"),
            object: nil
        )
    }

    // 检查是否可以访问高级功能
    func canAccessPremiumFeature() async -> Bool {
        return isLifetimeMember
    }

    // 记录购买
    func recordPurchase() {
        Task {
            await handlePurchase()
        }
    }

    // 手动恢复购买
    func manuallyRestorePurchases() async -> Bool {
        guard isStoreReady, let _ = self.storeHelper else {
            logger.warning("⚠️ StoreHelper 未就绪，无法恢复购买")
            return false
        }

        logger.info("开始手动恢复购买...")
        restoreRetryCount = 0
        return await verifyAndHandleTransactions()
    }

    // 检查并处理购买状态
    func checkAndRestorePurchases() async {
        guard isStoreReady && storeHelper != nil else {
            logger.warning("⚠️ StoreHelper 未就绪，跳过恢复购买")
            return
        }

        // 总是检查服务器状态，不仅仅依赖本地记录
        logger.info("开始检查购买状态...")
        await updateLifetimeMemberStatus()

        // 如果服务器检查后仍然没有购买记录，尝试恢复
        if !isLifetimeMember {
            logger.info("服务器检查无购买记录，开始自动恢复")
            restoreRetryCount = 0
            await restorePurchases()
        }
    }

    // 恢复购买的方法
    private func restorePurchases() async {
        logger.info("===== 开始自动恢复购买 (第\(self.restoreRetryCount + 1)次尝试) =====")

        let hasValidPurchase = await verifyAndHandleTransactions()
        if !hasValidPurchase {
            if restoreRetryCount < maxRetries {
                restoreRetryCount += 1
                logger.info("将在 \(self.retryDelay) 秒后进行第 \(self.restoreRetryCount + 1) 次重试")
                do {
                    try await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                    await restorePurchases()
                } catch {
                    logger.error("❌ 休眠失败，立即重试: \(error.localizedDescription)")
                    await restorePurchases()
                }
            } else {
                logger.warning("⚠️ 已达到最大重试次数")
                restoreRetryCount = 0
            }
        }
    }

    // 验证和处理交易的私有方法
    private func verifyAndHandleTransactions() async -> Bool {
        logger.info("开始获取交易记录...")
        var hasValidPurchase = false
        var transactionCount = 0

        for await result in Transaction.currentEntitlements {
            transactionCount += 1
            logger.info("发现一条交易记录，正在验证...")
            if case .verified(let transaction) = result {
                logger.info("交易验证成功，ID: \(transaction.id)")
                logger.info("产品ID: \(transaction.productID)")

                if transaction.productID == Constants.Store.premiumProductId {
                    logger.info("✅ 发现有效购买: \(transaction.productID)")
                    hasValidPurchase = true
                    await transaction.finish()
                    logger.info("交易完成")
                    await self.handlePurchase()
                    logger.info("会员状态已更新")
                    break
                }
            }
        }

        if transactionCount > 0 && !hasValidPurchase {
            logger.warning("⚠️ 已查询到 \(transactionCount) 条交易记录，但均无效")
        }



        return hasValidPurchase
    }

    @objc private func handlePurchaseCompletion(_ notification: Notification) {
        logger.info("收到购买完成通知")
    }

    private func updateLifetimeMemberStatus() async {
        guard isStoreReady else {
            logger.warning("⚠️ StoreHelper 未就绪，跳过状态更新")
            await MainActor.run {
                self.isLifetimeMember = false
            }
            return
        }

        guard let storeHelper = self.storeHelper else {
            logger.warning("⚠️ StoreHelper 未初始化")
            await MainActor.run {
                self.isLifetimeMember = false
                logger.info("发送 MembershipStatusUpdated 通知: isLifetimeMember = false (StoreHelper 未初始化)")
                NotificationCenter.default.post(
                    name: NSNotification.Name("MembershipStatusUpdated"),
                    object: nil,
                    userInfo: ["isLifetimeMember": false]
                )
            }
            return
        }

        logger.info("开始检查会员商品状态...")
        let hasPremium = (try? await storeHelper.isPurchased(productId: Constants.Store.premiumProductId)) ?? false

        logger.info("会员商品状态检查结果:")
        logger.info("- Premium (oldfriends): \(hasPremium)")
        logger.info("- 本地状态: \(self.isLifetimeMember)")
        logger.info("- 产品ID: \(Constants.Store.premiumProductId)")

        let isMember = hasPremium

        // 总是更新状态，确保与服务器同步
        await MainActor.run {
            if self.isLifetimeMember != isMember {
                logger.info("更新会员状态: \(self.isLifetimeMember) -> \(isMember)")
                self.isLifetimeMember = isMember

                if isMember {
                    logger.info("保存购买状态到本地存储")
                    savePurchaseStatus()
                } else {
                    logger.info("清除本地购买状态")
                    clearPurchaseStatus()
                }

                logger.info("发送 MembershipStatusUpdated 通知: isLifetimeMember = \(isMember)")
                NotificationCenter.default.post(
                    name: NSNotification.Name("MembershipStatusUpdated"),
                    object: nil,
                    userInfo: ["isLifetimeMember": isMember]
                )
            } else {
                logger.info("会员状态无变化，保持: \(isMember)")
            }
        }
    }

    // 保存购买状态到本地
    private func savePurchaseStatus() {
        logger.info("保存购买状态到本地")
        UserDefaults.standard.set(true, forKey: purchaseStatusKey)
        UserDefaults.standard.synchronize()
    }

    // 从本地加载购买状态
    private func loadPurchaseStatus() {
        logger.info("从本地加载购买状态")
        Task { @MainActor in
            isLifetimeMember = UserDefaults.standard.bool(forKey: purchaseStatusKey)
            logger.info("本地购买状态: \(self.isLifetimeMember ? "已购买" : "未购买")")
        }
    }

    // 检查本地购买状态
    private func hasLocalPurchaseRecord() -> Bool {
        return UserDefaults.standard.bool(forKey: purchaseStatusKey)
    }

    // 清除本地购买状态
    private func clearPurchaseStatus() {
        logger.info("清除本地购买状态")
        UserDefaults.standard.removeObject(forKey: purchaseStatusKey)
        UserDefaults.standard.synchronize()
    }

    func canPlayEpisode(seasonNumber: Int, episodeNumber: Int) -> Bool {
        // 第一季前两集免费
        if seasonNumber == 1 && episodeNumber <= 2 {
            return true
        }

        // 如果是终身会员，不显示购买提示
        if isLifetimeMember {
                showPurchaseAlert = false
        }

        return isLifetimeMember
    }

    func handlePurchase() async {
        await MainActor.run {
            isPurchasing = true
        }

        // 验证购买状态而不是直接设置
        await updateLifetimeMemberStatus()

        await MainActor.run {
            isPurchasing = false
        }
    }
}
