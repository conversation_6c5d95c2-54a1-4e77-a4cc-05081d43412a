import Foundation

struct FavoriteSentence: Identifiable, Hashable, Codable {
    let english: String
    let chinese: String
    let seasonNumber: Int
    let episodeNumber: Int
    let episodeTitle: String
    let timestamp: Date
    let startTime: TimeInterval
    let endTime: TimeInterval
    let audioFileName: String
    
    var id: String { english }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(english)
    }
    
    static func == (lhs: FavoriteSentence, rhs: FavoriteSentence) -> Bool {
        lhs.english == rhs.english
    }
}

public class FavoriteManager: ObservableObject {
    private let database = FavoriteDatabase.shared
    
    @Published var favoriteWords: Set<String> {
        didSet {
            saveFavoriteWords()
        }
    }
    
    @Published private var favoriteSentences: [FavoriteSentence] = []
    
    public init() {
        self.favoriteWords = database.loadFavoriteWords()
        
        // 从数据库加载收藏的句子并转换为 FavoriteSentence 类型
        let loadedSentences = database.loadFavoriteSentences()
        self.favoriteSentences = loadedSentences.map { sentence in
            FavoriteSentence(
                english: sentence.english,
                chinese: sentence.chinese,
                seasonNumber: sentence.seasonNumber,
                episodeNumber: sentence.episodeNumber,
                episodeTitle: sentence.episodeTitle,
                timestamp: sentence.timestamp,
                startTime: sentence.startTime,
                endTime: sentence.endTime,
                audioFileName: sentence.audioFileName
            )
        }
    }
    
    func toggleWordFavorite(_ word: String, seasonNumber: Int? = nil, episodeNumber: Int? = nil, episodeTitle: String? = nil) {
        if favoriteWords.contains(word) {
            favoriteWords.remove(word)
            database.removeWord(word)
        } else {
            favoriteWords.insert(word)
            if let season = seasonNumber, let episode = episodeNumber, let title = episodeTitle {
                database.saveWord(word, seasonNumber: season, episodeNumber: episode, episodeTitle: title)
            }
        }
    }
    
    func toggleSentenceFavorite(
        english: String,
        chinese: String,
        seasonNumber: Int,
        episodeNumber: Int,
        episodeTitle: String,
        timestamp: Date,
        startTime: TimeInterval,
        endTime: TimeInterval,
        audioFileName: String
    ) {
        if let index = favoriteSentences.firstIndex(where: { $0.english == english }) {
            favoriteSentences.remove(at: index)
            database.removeSentence(english)
        } else {
            let newFavorite = FavoriteSentence(
                english: english,
                chinese: chinese,
                seasonNumber: seasonNumber,
                episodeNumber: episodeNumber,
                episodeTitle: episodeTitle,
                timestamp: timestamp,
                startTime: startTime,
                endTime: endTime,
                audioFileName: audioFileName
            )
            favoriteSentences.append(newFavorite)
            database.saveSentence(
                english,
                seasonNumber: seasonNumber,
                episodeNumber: episodeNumber,
                episodeTitle: episodeTitle,
                chineseText: chinese,
                startTime: startTime,
                endTime: endTime,
                audioFileName: audioFileName
            )
        }
        objectWillChange.send()
    }
    
    func isWordFavorited(_ word: String) -> Bool {
        favoriteWords.contains(word)
    }
    
    func isSentenceFavorited(_ english: String) -> Bool {
        favoriteSentences.contains(where: { $0.english == english })
    }
    
    private func saveFavoriteWords() {
        // 数据已经在 toggleWordFavorite 中保存到数据库
    }
    
    private func saveFavoriteSentences() {
        // 数据已经在 toggleSentenceFavorite 中保存到数据库
    }
    
    func getWordDetails(_ word: String) -> (seasonNumber: Int, episodeNumber: Int, episodeTitle: String)? {
        let details = database.getWordDetails(word)
        return details
    }
    
    func getSentenceDetails(_ sentence: String) -> (seasonNumber: Int, episodeNumber: Int, episodeTitle: String, chineseText: String)? {
        let details = database.getSentenceDetails(sentence)
        return details
    }
    
    func getAllFavoriteSentences() -> [FavoriteSentence] {
        favoriteSentences.sorted(by: { $0.timestamp > $1.timestamp })
    }
} 