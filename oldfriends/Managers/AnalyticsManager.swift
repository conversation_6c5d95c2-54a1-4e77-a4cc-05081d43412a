import Foundation
import FirebaseAnalytics

class AnalyticsManager {
    static let shared = AnalyticsManager()
    
    private init() {}
    
    // MARK: - Screen Views
    
    func logScreenView(_ screenName: String, parameters: [String: Any]? = nil) {
        var params: [String: Any] = ["screen_name": screenName]
        if let additionalParams = parameters {
            params.merge(additionalParams) { (_, new) in new }
        }
        Analytics.logEvent(AnalyticsEventScreenView, parameters: params)
    }
    
    // MARK: - Screen View Events
    
    func logPlayerScreenView() {
        logScreenView("player")
    }
    
    func logShowListScreenView() {
        logScreenView("show_list")
    }
    
    func logFavoriteWordsScreenView() {
        logScreenView("favorite_words")
    }
    
    func logFavoriteSentencesScreenView() {
        logScreenView("favorite_sentences")
    }
    
    func logPurchaseAlertScreenView() {
        logScreenView("purchase_alert")
    }
    
    // MARK: - Purchase Events
    
    func logPurchaseButtonClick() {
        Analytics.logEvent("purchase_button_click", parameters: nil)
    }
    
    func logRestorePurchaseClick() {
        Analytics.logEvent("restore_purchase_click", parameters: nil)
    }
    
    func logCancelPurchaseClick() {
        Analytics.logEvent("cancel_purchase_click", parameters: nil)
    }
    
    // MARK: - Show List Events
    
    func logSeasonButtonClick() {
        Analytics.logEvent("season_button_click", parameters: nil)
    }
    
    func logEpisodeItemClick() {
        Analytics.logEvent("episode_item_click", parameters: nil)
    }
    
    func logShowListCloseClick() {
        Analytics.logEvent("show_list_close_click", parameters: nil)
    }
    
    func logShowListUnlockClick() {
        Analytics.logEvent("show_list_unlock_click", parameters: nil)
    }
    
    func logShowListMembershipStatusClick() {
        Analytics.logEvent("show_list_membership_status_click", parameters: nil)
    }
    
    // MARK: - Upgrade Toast Events
    
    func logUpgradeToastClick() {
        Analytics.logEvent("upgrade_toast_click", parameters: nil)
    }
    
    func logUpgradeToastCloseClick() {
        Analytics.logEvent("upgrade_toast_close_click", parameters: nil)
    }
    
    // MARK: - Export Events
    
    func logExportPDFClick() {
        Analytics.logEvent("export_pdf_click", parameters: nil)
    }
    
    func logExportFavoriteWordsClick() {
        Analytics.logEvent("export_favorite_words_click", parameters: nil)
    }
    
    func logExportFavoriteSentencesClick() {
        Analytics.logEvent("export_favorite_sentences_click", parameters: nil)
    }
} 