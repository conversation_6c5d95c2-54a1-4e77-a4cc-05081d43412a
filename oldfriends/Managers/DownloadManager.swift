import Foundation
import OSLog
import CommonCrypto
import BackgroundTasks
import AVFoundation

class DownloadManager: NSObject {
    static let shared = DownloadManager()
    
    private static var isBackgroundTaskRegistered = false
    
    private var backgroundSession: URLSession!
    private var downloadTasks: [String: URLSessionDownloadTask] = [:]
    private var completionHandlers: [String: () -> Void] = [:]
    private var progressHandlers: [String: (Double) -> Void] = [:]
    
    // 用于存储下载状态
    private let defaults = UserDefaults.standard
    private let downloadedFilesKey = "DownloadedFiles"
    
    // 添加存储下载开始时间的字典
    private var downloadStartTimes: [String: Date] = [:]
    
    // 添加重试相关属性
    private var downloadRetryCount: [String: Int] = [:]
    private let maxRetryCount = 3
    private let initialRetryDelay: TimeInterval = 1.0
    
    // 添加Logger
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "DownloadManager")
    
    private override init() {
        super.init()
        setupBackgroundSession()
    }
    
    // MARK: - Debug Logger
    private func debugLog(_ message: String, type: OSLogType = .info) {
        #if DEBUG
        switch type {
        case .info:
            logger.info("\(message)")
        case .error:
            logger.error("\(message)")
        case .debug:
            logger.debug("\(message)")
        case .fault:
            logger.fault("\(message)")
        default:
            logger.info("\(message)")
        }
        #endif
    }
    
    private func setupBackgroundSession() {
        let config = URLSessionConfiguration.background(withIdentifier: "com.dramalingo.download")
        config.isDiscretionary = false  // 修改为 false，确保立即开始下载
        config.sessionSendsLaunchEvents = true
        config.allowsCellularAccess = true  // 添加允许蜂窝网络访问
        config.waitsForConnectivity = true  // 添加等待连接
        backgroundSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }
    
    static func registerBackgroundTasks() {
        guard !isBackgroundTaskRegistered else { return }
        
        BGTaskScheduler.shared.register(forTaskWithIdentifier: "com.dramalingo.download", using: nil) { task in
            shared.handleBackgroundTask(task as! BGProcessingTask)
        }
        
        isBackgroundTaskRegistered = true
        //// print("[DownloadManager] 后台任务注册成功")
    }
    
    func scheduleBackgroundTask() {
        let request = BGProcessingTaskRequest(identifier: "com.dramalingo.download")
        request.requiresNetworkConnectivity = true
        request.requiresExternalPower = false
        
        do {
            try BGTaskScheduler.shared.submit(request)
            //// print("[DownloadManager] 成功调度后台下载任务")
        } catch {
            //// print("[DownloadManager] 调度后台下载任务失败: \(error.localizedDescription)")
        }
    }
    
    private func handleBackgroundTask(_ task: BGProcessingTask) {
        // 设置任务过期处理
        task.expirationHandler = {
            task.setTaskCompleted(success: false)
        }
        
        // 处理未完成的下载任务
        let pendingTasks = downloadTasks.values
        if pendingTasks.isEmpty {
            task.setTaskCompleted(success: true)
            // 重新调度下一次任务
            scheduleBackgroundTask()
            return
        }
        
        // 等待所有下载任务完成
        let group = DispatchGroup()
        for downloadTask in pendingTasks {
            group.enter()
            downloadTask.resume()
            
            // 在下载任务完成时离开组
            downloadTask.delegate = self
        }
        
        group.notify(queue: .main) {
            task.setTaskCompleted(success: true)
            // 重新调度下一次任务
            self.scheduleBackgroundTask()
        }
    }
    
    // 检查文件是否已下载
    func isFileDownloaded(_ fileName: String) -> Bool {
        let downloadedFiles = defaults.stringArray(forKey: downloadedFilesKey) ?? []
        return downloadedFiles.contains(fileName)
    }
    
    // 获取本地文件URL
    func getLocalFileURL(for fileName: String) -> URL? {
        guard isFileDownloaded(fileName) else { return nil }
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileType = getFileType(from: fileName)
        return documentsPath.appendingPathComponent(fileType).appendingPathComponent(fileName)
    }
    
    // 开始下载文件
    func startDownload(
        fileName: String,
        fileType: String,
        progressHandler: ((Double) -> Void)? = nil,
        completion: (() -> Void)? = nil
    ) {
        debugLog("开始下载文件: \(fileName), 类型: \(fileType)")
        
        downloadStartTimes[fileName] = Date()
        
        if isFileDownloaded(fileName),
           let localURL = getLocalFileURL(for: fileName) {
            
            if verifyDownloadedFile(at: localURL, originalFileName: fileName) {
                debugLog("文件已存在且验证通过，跳过下载: \(fileName)")
                completion?()
                return
            } else {
                debugLog("文件验证失败，需要重新下载: \(fileName)")
                var downloadedFiles = defaults.stringArray(forKey: downloadedFilesKey) ?? []
                downloadedFiles.removeAll { $0 == fileName }
                defaults.set(downloadedFiles, forKey: downloadedFilesKey)
            }
        }
        
        if downloadTasks[fileName] != nil {
            debugLog("下载任务已存在: \(fileName)")
            completion?()
            return
        }
        
        let ossURL = generateSignedURL(fileName: fileName, fileType: fileType)
        guard let url = URL(string: ossURL) else {
            debugLog("错误: 无效的URL: \(ossURL)", type: .error)
            return
        }
        
        debugLog("成功生成签名URL: \(ossURL)")
        
        let task = backgroundSession.downloadTask(with: url)
        downloadTasks[fileName] = task
        completionHandlers[fileName] = completion
        progressHandlers[fileName] = progressHandler
        task.resume()
        
        debugLog("下载任务已启动: \(fileName)")
    }
    
    // 添加生成签名URL的方法
    private func generateSignedURL(fileName: String, fileType: String) -> String {
        let contentPath = fileType == "audio" ? 
            "audio/MP3/\(fileName)" : 
            "srt/\(fileName)"
        
        let currentTime = Int(Date().timeIntervalSince1970)
        let expires = currentTime + 3600
        
//        let canonicalizedResource = "/\(OSSConfig.bucket)/\(contentPath)"
//            .addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? ""
        
        let stringToSign = [
            "GET",
            "",
            "",
            String(expires),
            "/\(OSSConfig.bucket)/\(contentPath)"
        ].joined(separator: "\n")
        
        //// print("\n[DownloadManager] OSS签名详情:")
        //// print("- 当前时间戳: \(currentTime)")
        //// print("- 过期时间戳: \(expires)")
        //// print("- 规范化资源: \(canonicalizedResource)")
        //// print("- 待签名字符串:\n\(stringToSign)")
        
        let signature = hmacSHA1(stringToSign, secret: OSSConfig.accessKeySecret)
        //// print("- 生成的签名: \(signature)")
        
        let encodedSignature = signature
            .addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        //// print("- URL编码后签名: \(encodedSignature)")
        
        return "\(OSSConfig.baseURL)/\(contentPath)?" +
            "OSSAccessKeyId=\(OSSConfig.accessKeyId)" +
            "&Expires=\(expires)" +
            "&Signature=\(encodedSignature)"
    }
    
    // HMAC-SHA1 签名实现
    private func hmacSHA1(_ string: String, secret: String) -> String {
        let keyData = secret.data(using: .utf8)!
        let messageData = string.data(using: .utf8)!
        var macData = Data(count: Int(CC_SHA1_DIGEST_LENGTH))
        
        keyData.withUnsafeBytes { keyPtr in
            messageData.withUnsafeBytes { messagePtr in
                macData.withUnsafeMutableBytes { macPtr in
                    CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA1),
                          keyPtr.baseAddress,
                          keyData.count,
                          messagePtr.baseAddress,
                          messageData.count,
                          macPtr.baseAddress)
                }
            }
        }
        
        return macData.base64EncodedString()
    }
    
    // 批载指定季的所有文件
    func downloadSeasonFiles(seasonNumber: Int) {
        guard let show = ShowManager.shared.loadShow() else { return }
        
        guard let season = show.seasons.first(where: { $0.number == seasonNumber }) else {
            //// print("[DownloadManager] 未找到季数: \(seasonNumber)")
            return
        }
        
        for episode in season.episodes {
            startDownload(fileName: "\(episode.audioName).mp3", fileType: "audio")
            
            startDownload(fileName: "\(episode.subtitleName).srt", fileType: "srt")
        }
    }
    
    // 标记文件为已下载
    private func markFileAsDownloaded(_ fileName: String) {
        var downloadedFiles = defaults.stringArray(forKey: downloadedFilesKey) ?? []
        if !downloadedFiles.contains(fileName) {
            downloadedFiles.append(fileName)
            defaults.set(downloadedFiles, forKey: downloadedFilesKey)
        }
    }
    
    // 清理下载任务
    private func cleanupDownloadTask(for fileName: String) {
        downloadTasks.removeValue(forKey: fileName)
        completionHandlers.removeValue(forKey: fileName)
    }
    
    // 添加获取文件类型的辅助方法
    private func getFileType(from fileName: String) -> String {
        if fileName.hasSuffix(".mp3") {
            return "audio"
        } else if fileName.hasSuffix(".srt") {
            return "srt"
        }
        return "others"
    }
    
    func cancelDownload() {
        debugLog("用户取消下载任务")
        
        for (_, task) in downloadTasks {
            task.cancel()
        }
        
        downloadTasks.removeAll()
        completionHandlers.removeAll()
        progressHandlers.removeAll()
        downloadStartTimes.removeAll()
    }
    
    func validateFile(fileName: String) -> Bool {
        guard let fileURL = getLocalFileURL(for: fileName) else {
            debugLog("验证文件失败，无法获取文件URL: \(fileName)", type: .error)
            return false
        }
        return verifyDownloadedFile(at: fileURL, originalFileName: fileName)
    }
    
    private func verifyDownloadedFile(at location: URL, originalFileName: String) -> Bool {
        debugLog("开始验证文件: \(originalFileName)")
        
        if originalFileName.hasSuffix(".mp3") {
            do {
                debugLog("开始验证 MP3 文件...")
                
                let attributes = try FileManager.default.attributesOfItem(atPath: location.path)
                let fileSize = attributes[.size] as? Int64 ?? 0
                debugLog("- 文件大小: \(fileSize) bytes")
                
                guard fileSize > 1024 * 100 else {
                    debugLog("MP3文件验证失败: 文件过小 (\(fileSize) bytes)", type: .error)
                    return false
                }
                
                let handle = try FileHandle(forReadingFrom: location)
                defer {
                    try? handle.close()
                }
                
                let headerData = try handle.read(upToCount: 10)
                guard let header = headerData,
                      header.count >= 3 else {
                    debugLog("MP3文件验证失败: 无法读取文件头", type: .error)
                    return false
                }

                // 打印文件头信息用于调试
                let headerHex = header.prefix(10).map { String(format: "%02X", $0) }.joined(separator: " ")
                debugLog("文件头信息: \(headerHex)")

                let hasID3 = (header[0] == 0x49 && header[1] == 0x44 && header[2] == 0x33) // "ID3"
                let hasMPEGSync = (header[0] == 0xFF && (header[1] & 0xE0) == 0xE0) // MPEG sync

                debugLog("ID3标签检查: \(hasID3)")
                debugLog("MPEG同步检查: \(hasMPEGSync)")

                guard hasID3 || hasMPEGSync else {
                    debugLog("MP3文件验证失败: 无效的文件头", type: .error)
                    debugLog("期望: ID3标签(49 44 33) 或 MPEG同步(FF Ex)", type: .error)
                    debugLog("实际: \(headerHex)", type: .error)
                    return false
                }
                
                let audioFile = try AVAudioFile(forReading: location)
                let format = audioFile.processingFormat
                
                guard format.sampleRate > 0,
                      format.channelCount > 0,
                      audioFile.length > 0 else {
                    debugLog("MP3文件验证失败: 无效的音频格式", type: .error)
                    return false
                }
                
                let player = try AVAudioPlayer(contentsOf: location)
                guard player.prepareToPlay() else {
                    debugLog("MP3文件验证失败: 音频准备播放失败", type: .error)
                    return false
                }
                
                debugLog("MP3文件验证通过:")
                debugLog("- 文件大小: \(fileSize) bytes")
                debugLog("- 采样率: \(format.sampleRate) Hz")
                debugLog("- 声道数: \(format.channelCount)")
                debugLog("- 音频时长: \(player.duration) 秒")
                return true
                
            } catch {
                debugLog("MP3文件验证出错:", type: .error)
                debugLog("- 文件名: \(originalFileName)", type: .error)
                debugLog("- 错误信息: \(error.localizedDescription)", type: .error)
                debugLog("- 错误详情: \(error)", type: .error)
                return false
            }
        }
        
        if originalFileName.hasSuffix(".srt") {
            do {
                let content = try String(contentsOf: location, encoding: .utf8)
                
                if content.contains("<Error>") || content.contains("<Code>") {
                    debugLog("SRT文件验证失败: 包含错误响应", type: .error)
                    debugLog("错误内容:\n\(content)", type: .error)
                    return false
                }
                
                let lines = content.components(separatedBy: .newlines)
                guard !lines.isEmpty else {
                    debugLog("SRT文件验证失败: 文件为空", type: .error)
                    return false
                }
                
                if let firstLine = lines.first?.trimmingCharacters(in: .whitespacesAndNewlines),
                   Int(firstLine) == nil {
                    debugLog("SRT文件验证失败: 格式不正确", type: .error)
                    return false
                }
                
                return true
            } catch {
                debugLog("SRT文件验证失败: \(error.localizedDescription)", type: .error)
                return false
            }
        }
        
        return true
    }
    
    private func getRetryDelay(for attempt: Int) -> TimeInterval {
        return TimeInterval(pow(2.0, Double(attempt - 1)))
    }
    
    private func downloadWithRetry(fileName: String, fileType: String, maxRetries: Int = 3) async {
        var retryCount = 0
        
        func retry() async {
            retryCount += 1
            let delay = getRetryDelay(for: retryCount)
            
            //// print("[DownloadManager] 开始第 \(retryCount) 次重试")
            
            try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            
            return await withCheckedContinuation { continuation in
                self.progressHandlers[fileName]?(0.0)
                
                self.startDownload(fileName: fileName, fileType: fileType) { progress in
                    DispatchQueue.main.async {
                        self.progressHandlers[fileName]?(progress)
                    }
                } completion: {
                    Task {
                        do {
                            if try await self.validateDownloadedFile(fileName) {
                                //// print("[DownloadManager] 下载成功")
                                DispatchQueue.main.async {
                                    self.progressHandlers[fileName]?(1.0)
                                }
                                continuation.resume()
                            } else if retryCount < maxRetries {
                                //// print("[DownloadManager] 文件验证失败，\(delay)秒后重试")
                                await retry()
                            } else {
                                //// print("[DownloadManager] 达到最大重试次数，下载失败")
                                continuation.resume()
                            }
                        } catch {
                            if retryCount < maxRetries {
                                //// print("[DownloadManager] 验证错误: \(error.localizedDescription)，准备重试")
                                await retry()
                            } else {
                                //// print("[DownloadManager] 验证错误后达到最大重试次数")
                                continuation.resume()
                            }
                        }
                    }
                }
            }
        }
        
        await retry()
    }
    
    private func validateDownloadedFile(_ fileName: String) async throws -> Bool {
        guard let fileURL = getLocalFileURL(for: fileName) else {
            throw DownloadError.fileNotFound
        }
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            throw DownloadError.fileNotFound
        }
        
        if fileName.hasSuffix(".mp3") {
            return try await validateAudioFile(at: fileURL)
        } else if fileName.hasSuffix(".srt") {
            return try await validateSubtitleFile(at: fileURL)
        }
        
        throw DownloadError.unsupportedFileType
    }
    
    private func validateAudioFile(at fileURL: URL) async throws -> Bool {
        do {
            let player = try AVAudioPlayer(contentsOf: fileURL)
            guard player.duration > 0 else {
                throw DownloadError.invalidAudioFile
            }
            let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
            guard (attributes[.size] as? UInt64 ?? 0) > 1024 else {
                throw DownloadError.fileTooSmall
            }
            return true
        } catch {
            throw DownloadError.audioValidationFailed(error)
        }
    }
    
    private func validateSubtitleFile(at fileURL: URL) async throws -> Bool {
        do {
            let content = try String(contentsOf: fileURL, encoding: .utf8)
            guard !content.isEmpty else {
                throw DownloadError.emptyFile
            }
            let lines = content.components(separatedBy: .newlines)
            guard lines.count > 3 else {
                throw DownloadError.invalidSubtitleFormat
            }
            guard !content.contains("<Error>") && !content.contains("404 Not Found") else {
                throw DownloadError.invalidContent
            }
            return true
        } catch {
            throw DownloadError.subtitleValidationFailed(error)
        }
    }
    
    enum DownloadError: Error {
        case fileNotFound
        case unsupportedFileType
        case invalidAudioFile
        case invalidSubtitleFormat
        case emptyFile
        case fileTooSmall
        case invalidContent
        case audioValidationFailed(Error)
        case subtitleValidationFailed(Error)
    }
}

extension DownloadManager: URLSessionDownloadDelegate {
    func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        guard let fileName = downloadTask.originalRequest?.url?.lastPathComponent else { return }
        
        let fileType = getFileType(from: fileName)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let typeDirectory = documentsPath.appendingPathComponent(fileType)
        let destinationURL = typeDirectory.appendingPathComponent(fileName)
        
        debugLog("下载完成:")
        debugLog("- 目标文件: \(fileName)")
        debugLog("- 文件类型: \(fileType)")
        debugLog("- 临时位置: \(location.path)")
        debugLog("- 最终位置: \(destinationURL.path)")
        
        do {
            debugLog("开始验证下载文件: \(fileName)")
            guard verifyDownloadedFile(at: location, originalFileName: fileName) else {
                debugLog("文件验证失败，准备重试: \(fileName)", type: .error)
                handleDownloadRetry(fileName: fileName, fileType: fileType)
                return
            }
            
            if !FileManager.default.fileExists(atPath: typeDirectory.path) {
                try FileManager.default.createDirectory(at: typeDirectory,
                                                     withIntermediateDirectories: true)
            }
            
            if FileManager.default.fileExists(atPath: destinationURL.path) {
                try? FileManager.default.removeItem(at: destinationURL)
                debugLog("已删除已存在的文件: \(fileName)")
            }
            
            _ = try FileManager.default.replaceItemAt(destinationURL, 
                                                    withItemAt: location)
            
            debugLog("文件已保存到最终位置:")
            debugLog("- 文件名: \(fileName)")
            debugLog("- 保存位置: \(destinationURL.path)")
            
            markFileAsDownloaded(fileName)
            
            DispatchQueue.main.async {
                self.downloadTasks[fileName] = nil
                self.completionHandlers[fileName]?()
                self.completionHandlers[fileName] = nil
                self.progressHandlers[fileName] = nil
                self.downloadRetryCount.removeValue(forKey: fileName)
            }
        } catch {
            debugLog("文件处理失败:", type: .error)
            debugLog("- 文件名: \(fileName)", type: .error)
            debugLog("- 错误信息: \(error.localizedDescription)", type: .error)
            
            handleDownloadRetry(fileName: fileName, fileType: fileType)
        }
        
        debugLog("URLSession下载完成回调触发")
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        guard let url = task.originalRequest?.url else { return }
        let fileName = url.lastPathComponent
        
        downloadStartTimes.removeValue(forKey: fileName)
        
        if let error = error {
            debugLog("===== 下载失败 =====", type: .error)
            debugLog("请求信息:", type: .error)
            debugLog("- 文件名: \(fileName)", type: .error)
            debugLog("- 完整URL: \(url)", type: .error)
            debugLog("- HTTP状态码: \((task.response as? HTTPURLResponse)?.statusCode ?? -1)", type: .error)
            debugLog("错误信息: \(error.localizedDescription)", type: .error)
        }
        
        DispatchQueue.main.async {
            self.cleanupDownloadTask(for: fileName)
        }
    }
    
    func urlSession(
        _ session: URLSession,
        downloadTask: URLSessionDownloadTask,
        didWriteData bytesWritten: Int64,
        totalBytesWritten: Int64,
        totalBytesExpectedToWrite: Int64
    ) {
        guard let url = downloadTask.originalRequest?.url,
              let fileName = url.lastPathComponent as String?,
              let progressHandler = progressHandlers[fileName] else {
            return
        }
        
        let progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)
        
        // 每10%记录一次日志
        if Int(progress * 10) % 2 == 0 {
            debugLog("下载进度: \(Int(progress * 100))%, 已下载: \(totalBytesWritten/1024)KB, 总大小: \(totalBytesExpectedToWrite/1024)KB")
        }
        
        DispatchQueue.main.async {
            progressHandler(progress)
        }
    }
    
    private func handleDownloadRetry(fileName: String, fileType: String) {
        let retryCount = (downloadRetryCount[fileName] ?? 0) + 1
        downloadRetryCount[fileName] = retryCount
        
        if retryCount <= maxRetryCount {
            debugLog("开始第 \(retryCount) 次重试")
            let delay = getRetryDelay(for: retryCount)
            
            let originalProgressHandler = progressHandlers[fileName]
            let originalCompletionHandler = completionHandlers[fileName]
            
            DispatchQueue.main.async {
                originalProgressHandler?(0.0)
            }
            
            DispatchQueue.global().asyncAfter(deadline: .now() + delay) { [weak self] in
                guard let self = self else { return }
                self.startDownload(
                    fileName: fileName,
                    fileType: fileType,
                    progressHandler: originalProgressHandler,
                    completion: originalCompletionHandler
                )
            }
        } else {
            debugLog("超过最大重试次数，下载失败", type: .error)
            DispatchQueue.main.async { [weak self] in
                self?.cleanupDownloadTask(for: fileName)
                self?.downloadRetryCount.removeValue(forKey: fileName)
                self?.progressHandlers[fileName]?(0.0)
                self?.completionHandlers[fileName]?()
            }
        }
    }
} 
