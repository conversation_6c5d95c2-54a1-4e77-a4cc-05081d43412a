// 文件名: ShowManager.swift
// 能力边界:
// 1. 负责从本地 JSON 文件加载剧集数据
// 2. 提供单例访问方式，确保全局唯一的数据加载入口
// 3. 处理剧集数据的解析和错误处理
// 4. 提供剧集数据的初始化加载
//
// 依赖关系:
// - 依赖模型:
//   └── Show (需要从 Models/ShowModel.swift 导入)
// - 依赖资源:
//   └── shows.json (位于 Resources 目录)

import Foundation



class ShowManager {
    static let shared = ShowManager()
    
    private init() {}
    
    func loadShow() -> Show? {
        if let url = Bundle.main.url(forResource: "shows", withExtension: "json"),
           let data = try? Data(contentsOf: url) {
            do {
                let decoder = JSONDecoder()
                let show = try decoder.decode(Show.self, from: data)
//                if let firstSeason = show.seasons.first,
//                   let firstEpisode = firstSeason.episodes.first {
                    // print("第一集信息: S\(firstSeason.number)E\(firstEpisode.number) - \(firstEpisode.title)")
//                }
                return show
            } catch {
                // print("解析剧集数据失败: \(error)")
                return nil
            }
        }
        // print("未找到 shows.json 文件")
        return nil
    }
} 
