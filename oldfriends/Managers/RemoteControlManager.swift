import Foundation
import MediaPlayer
import AVFoundation
import UIKit

// MARK: - RemoteControlManager
class RemoteControlManager {
    // MARK: - Properties
    private weak var playerViewModel: PlayerViewModel?

    // MARK: - Initialization
    init(playerViewModel: PlayerViewModel) {
        self.playerViewModel = playerViewModel
        setupRemoteTransportControls()
        setupAudioSession()
    }

    // MARK: - Setup Methods
    private func setupAudioSession() {
        do {
            // 设置音频会话类别为播放，并允许混音和后台播放
            try AVAudioSession.sharedInstance().setCategory(
                .playback,
                mode: .default,
                options: [.mixWithOthers, .allowBluetooth]
            )

            // 设置音频会话为活动状态
            try AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)

            // 注册中断通知
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioSessionInterruption),
                name: AVAudioSession.interruptionNotification,
                object: nil
            )

            // print("[RemoteControlManager] 音频会话设置成功")
        } catch {
            // print("[RemoteControlManager] 设置音频会话失败: \(error)")
        }
    }

    // 处理音频中断
    @objc private func handleAudioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        switch type {
        case .began:
            // 音频被中断（如来电），暂停播放
            // print("[RemoteControlManager] 音频中断开始")
            playerViewModel?.togglePlayPause()

        case .ended:
            // 中断结束，检查是否应该恢复播放
            guard let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt else { return }
            let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)

            // print("[RemoteControlManager] 音频中断结束")

            if options.contains(.shouldResume) {
                // 如果系统表明应该恢复播放，则恢复
                // print("[RemoteControlManager] 系统指示应该恢复播放")
                playerViewModel?.togglePlayPause()
            }

        @unknown default:
            break
        }
    }

    private func setupRemoteTransportControls() {
        // print("[RemoteControlManager] ===== 开始设置远程控制 =====")
        let commandCenter = MPRemoteCommandCenter.shared()

        // 配置播放/暂停命令
        commandCenter.playCommand.addTarget { [weak self] _ in
            // print("[RemoteControlManager] 收到播放命令")
            guard let playerViewModel = self?.playerViewModel else { return .commandFailed }
            if !playerViewModel.isPlaying {
                playerViewModel.togglePlayPause()
                self?.updateNowPlayingInfo()
            }
            return .success
        }

        commandCenter.pauseCommand.addTarget { [weak self] _ in
            // print("[RemoteControlManager] 收到暂停命令")
            guard let playerViewModel = self?.playerViewModel else { return .commandFailed }
            if playerViewModel.isPlaying {
                playerViewModel.togglePlayPause()
                self?.updateNowPlayingInfo()
            }
            return .success
        }

        // 使用 nextTrackCommand 替代 skipForwardCommand
        commandCenter.nextTrackCommand.addTarget { [weak self] _ in
            // print("[RemoteControlManager] 收到下一句命令")
            guard let playerViewModel = self?.playerViewModel else { return .commandFailed }
            if let currentSubtitle = playerViewModel.getCurrentSubtitle(),
               let currentIndex = playerViewModel.subtitles.firstIndex(where: { $0.id == currentSubtitle.id }),
               currentIndex < playerViewModel.subtitles.count - 1 {
                let nextSubtitle = playerViewModel.subtitles[currentIndex + 1]
                playerViewModel.playSubtitle(nextSubtitle)
                self?.updateNowPlayingInfo()
                // print("[RemoteControlManager] 已切换到下一句")
            }
            return .success
        }

        // 使用 previousTrackCommand 替代 skipBackwardCommand
        commandCenter.previousTrackCommand.addTarget { [weak self] _ in
            // print("[RemoteControlManager] 收到上一句命令")
            guard let playerViewModel = self?.playerViewModel else { return .commandFailed }
            if let currentSubtitle = playerViewModel.getCurrentSubtitle(),
               let currentIndex = playerViewModel.subtitles.firstIndex(where: { $0.id == currentSubtitle.id }),
               currentIndex > 0 {
                let previousSubtitle = playerViewModel.subtitles[currentIndex - 1]
                playerViewModel.playSubtitle(previousSubtitle)
                self?.updateNowPlayingInfo()
                // print("[RemoteControlManager] 已切换到上一句")
            }
            return .success
        }

        // 添加快进快退命令
        commandCenter.changePlaybackPositionCommand.addTarget { [weak self] event in
            guard let playerViewModel = self?.playerViewModel,
                  let event = event as? MPChangePlaybackPositionCommandEvent else {
                return .commandFailed
            }

            playerViewModel.seek(to: event.positionTime)
            self?.updateNowPlayingInfo()
            return .success
        }

        // print("[RemoteControlManager] ===== 远程控制设置完成 =====")
    }

    // MARK: - Public Methods
    func updateNowPlayingInfo() {
        let startTime = Date()
        guard let playerViewModel = playerViewModel else { return }

        var nowPlayingInfo = [String: Any]()

        // 设置标题和字幕
        if let currentSubtitle = playerViewModel.getCurrentSubtitle() {
            // 使用英文字幕作为标题，中文字幕作为副标题
            nowPlayingInfo[MPMediaItemPropertyTitle] = currentSubtitle.english
            nowPlayingInfo[MPMediaItemPropertyArtist] = currentSubtitle.chinese

            // 添加剧集信息
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = "S\(playerViewModel.currentSeasonNumber)E\(playerViewModel.currentEpisodeNumber) \(playerViewModel.currentEpisodeTitle)"
        } else {
            // 如果没有当前字幕，显示剧集信息
            nowPlayingInfo[MPMediaItemPropertyTitle] = "DramaLingo"
            nowPlayingInfo[MPMediaItemPropertyArtist] = "S\(playerViewModel.currentSeasonNumber)E\(playerViewModel.currentEpisodeNumber)"
            nowPlayingInfo[MPMediaItemPropertyAlbumTitle] = playerViewModel.currentEpisodeTitle
        }

        // 设置时长和当前播放位置
        nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = playerViewModel.totalTime
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = playerViewModel.currentTime

        // 设置播放速度
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = playerViewModel.isPlaying ? playerViewModel.playbackSpeed : 0.0
        nowPlayingInfo[MPNowPlayingInfoPropertyDefaultPlaybackRate] = playerViewModel.playbackSpeed

        // 设置封面图片 - 优先使用 NowPlayingArtwork
        if let image = UIImage(named: "NowPlayingArtwork") {
            let artwork = MPMediaItemArtwork(boundsSize: CGSize(width: 600, height: 600)) { size in
                return image.resized(to: size) ?? image
            }
            nowPlayingInfo[MPMediaItemPropertyArtwork] = artwork
        }

        // 更新控制中心信息
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo

        let timeElapsed = Date().timeIntervalSince(startTime)
        if timeElapsed > 0.1 {
            // print("[RemoteControlManager] 更新播放信息耗时: \(timeElapsed)秒")
        }
    }

    // MARK: - Cleanup
    func cleanup() {
        do {
            try AVAudioSession.sharedInstance().setActive(false)
        } catch {
            // print("[RemoteControlManager] 停用音频会话失败: \(error)")
        }
    }

    deinit {
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
    }
}