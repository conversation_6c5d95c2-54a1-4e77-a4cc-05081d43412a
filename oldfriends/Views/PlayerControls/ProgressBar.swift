import SwiftUI
import Foundation

// 修进度条组
struct ProgressBar: View {
    let value: TimeInterval
    let total: TimeInterval
    let onSeek: (TimeInterval) -> Void
    @Environment(\.colorScheme) var colorScheme
    
    @State private var isDragging = false
    @State private var dragValue: TimeInterval = 0
    @State private var lastDragUpdate = Date()
    private let dragThrottleInterval: TimeInterval = 0.1
    
    var body: some View {
        VStack(spacing: 1) {
            // 时间显示和进度条在同一行，确保垂直居中对齐
            HStack(alignment: .center) {
                // 开始时间
                Text(formatTime(isDragging ? dragValue : value))
                    .font(.system(size: 9))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 42, alignment: .leading)
                
                // 进度条
                GeometryReader { geometry in
                    // 使用 ZStack 来确保垂直居中
                    ZStack {
                        // 使用 VStack 来扩大可点击区域
                        VStack(spacing: 0) {
                            Spacer()
                            ZStack(alignment: .leading) {
                                // 背景条
                                Capsule()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(height: isDragging ? 4 : 2)
                                
                                // 进度条
                                Capsule()
                                    .fill(ThemeColors.primary)
                                    .frame(
                                        width: geometry.size.width * CGFloat(total > 0 ? (isDragging ? dragValue : value) / total : 0),
                                        height: isDragging ? 4 : 2
                                    )
                                    .animation(.easeInOut(duration: 0.2), value: isDragging)
                                
                                // 拖动指示器（只在拖动时显示）
                                if isDragging {
                                    Circle()
                                        .fill(ThemeColors.primary)
                                        .frame(width: 12, height: 12)
                                        .shadow(color: Color.black.opacity(0.2), radius: 2, x: 0, y: 1)
                                        .position(
                                            x: geometry.size.width * CGFloat(dragValue / total),
                                            y: isDragging ? 2 : 1 // 根据进度条高度调整垂直位置
                                        )
                                        .animation(.easeInOut(duration: 0.2), value: isDragging)
                                }
                            }
                            .frame(height: isDragging ? 4 : 2)
                            Spacer()
                        }
                        .frame(height: 14)
                    }
                    .frame(height: 14)
                    .contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                isDragging = true
                                let ratio = min(max(0, gesture.location.x / geometry.size.width), 1)
                                dragValue = total * Double(ratio)
                                
                                // 添加节流逻辑
                                let now = Date()
                                if now.timeIntervalSince(lastDragUpdate) >= dragThrottleInterval {
                                    onSeek(dragValue)
                                    lastDragUpdate = now
                                }
                            }
                            .onEnded { gesture in
                                let ratio = min(max(0, gesture.location.x / geometry.size.width), 1)
                                let newValue = total * Double(ratio)
                                onSeek(newValue)
                                
                                // 添加动画过渡
                                withAnimation(.easeOut(duration: 0.2)) {
                                    isDragging = false
                                    dragValue = newValue
                                }
                            }
                    )
                }
                
                // 结束时间
                Text(formatTime(total))
                    .font(.system(size: 9))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 42, alignment: .trailing)
            }
            .frame(height: 14)
        }
    }
    
    // 格式化时间为 00:00:00 格式
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}