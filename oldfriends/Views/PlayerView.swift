import SwiftUI
import AVFoundation
import StoreHelper
import Foundation

// MARK: - PlayerView
struct PlayerView: View {
    @EnvironmentObject var playerViewModel: PlayerViewModel
    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @State private var showSpeedOptions = false
    @State private var showSettings = false
    @State private var showRedownloadButton = false
    // 升级提示相关状态变量已移除
    @EnvironmentObject var wordSelectionManager: WordSelectionManager
    @EnvironmentObject var storeHelper: StoreHelper
    private let dictionaryDB = DictionaryDatabase.shared
    @StateObject private var membershipManager = MembershipManager.shared

    var body: some View {
        ZStack {
            BackgroundView(theme: playerViewModel.currentTheme, wordSelectionManager: wordSelectionManager)

            MainContentView(
                viewModel: playerViewModel,
                showSpeedOptions: $showSpeedOptions,
                showRedownloadButton: showRedownloadButton
            )

            // 播放速度选项窗
            if showSpeedOptions {
                SpeedOptionsPopover(
                    currentSpeed: playerViewModel.playbackSpeed,
                    theme: playerViewModel.currentTheme
                ) { speed in
                    // print("[PlayerView] 设置播放速度: \(speed)")
                    playerViewModel.setPlaybackSpeed(speed)
                    showSpeedOptions = false
                }
            }



            // 字典弹出层
            DictionaryOverlayView(
                wordSelectionManager: wordSelectionManager,
                dictionaryDB: dictionaryDB,
                theme: playerViewModel.currentTheme,
                viewModel: playerViewModel
            )

            // 下载进度遮罩
            if downloadViewModel.isDownloading {
                DownloadProgressView(
                    progress: downloadViewModel.downloadProgress,
                    currentFile: downloadViewModel.currentDownloadingFile
                )
            }

            // 升级提示已移除
        }
        .navigationBarHidden(true)
        .onAppear {
            // 添加页面访问埋点
            AnalyticsManager.shared.logPlayerScreenView()

            // 确保在视图出现时就初始化远程控制管理器
            if playerViewModel.remoteControlManager == nil {
                playerViewModel.initializeRemoteControl()
            }

            // 设置音频会话中断处理
            setupAudioSessionInterruption()

            checkResourceFiles()

            // 升级提示定时器已移除
        }
        .onDisappear {
            // 移除音频中断通知观察者
            NotificationCenter.default.removeObserver(self, name: AVAudioSession.interruptionNotification, object: nil)
        }
        .onChange(of: downloadViewModel.isDownloading) { newValue in
            if !newValue {
                // 下载完成后的处理
                // print("[PlayerView] 下载完成")
                checkResourceFiles()  // 检查资源文件状态
            }
        }
        // 会员状态变化处理已移除
    }

    // setupUpgradeReminder方法已移除

    private func checkResourceFiles() {
        // print("[PlayerView] 检查资源文件完整性")
        let isComplete = playerViewModel.areResourceFilesComplete()
        // print("[PlayerView] 资源文件是否完整: \(isComplete)")
        showRedownloadButton = !isComplete
    }

    private func setupAudioSessionInterruption() {
        NotificationCenter.default.addObserver(
            forName: AVAudioSession.interruptionNotification,
            object: nil,
            queue: .main
        ) { [playerViewModel] notification in
            guard let userInfo = notification.userInfo,
                  let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
                  let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
                return
            }

            switch type {
            case .began:
                // 音频被打断时，保存当前播放状态
                playerViewModel.handleAudioInterruptionBegan()

            case .ended:
                guard let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt else { return }
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)

                // 如果可以恢复播放，则恢复
                if options.contains(.shouldResume) {
                    playerViewModel.handleAudioInterruptionEnded()
                }

            @unknown default:
                break
            }
        }
    }
}

// MARK: - BackgroundView
private struct BackgroundView: View {
    let theme: AppTheme
    @ObservedObject var wordSelectionManager: WordSelectionManager

    var body: some View {
        ThemeColors.background(theme)
            .edgesIgnoringSafeArea(.all)
            .onAppear {
                // print("PlayerView - Current Theme: \(theme)")
            }
            .onChange(of: theme) { newValue in
                // print("PlayerView - Theme Changed to: \(newValue)")
            }
            .contentShape(Rectangle())
            .onTapGesture {
                // print("[PlayerView] 点击背景区域")
                if wordSelectionManager.selectedWord != nil {
                    // print("[PlayerView] 关闭查词气泡")
                    wordSelectionManager.selectedWord = nil
                }
            }
            .allowsHitTesting(wordSelectionManager.selectedWord != nil)
    }
}

// MARK: - MainContentView
private struct MainContentView: View {
    @ObservedObject var viewModel: PlayerViewModel
    @Binding var showSpeedOptions: Bool
    let showRedownloadButton: Bool

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .bottom) {
                // 主内容区域
                VStack(spacing: 0) {
                    // 顶部导航栏
                    PlayerTopBarView(theme: viewModel.currentTheme)
                        .allowsHitTesting(true)

                    // 字幕区域
                    SubtitleAreaView(
                        viewModel: viewModel,
                        showRedownloadButton: showRedownloadButton
                    )
                    .frame(maxHeight: .infinity)

                    // 底部占位，为控制栏留出空间
                    Color.clear
                        .frame(height: geometry.safeAreaInsets.bottom + 50) // 为控制栏和安全区域预留空间
                }

                // 底部控制栏 - 使用自定义背景
                VStack(spacing: 0) {
                    // 使用修改后的PlayerControlBar，移除其自带背景
                    PlayerControlBar(viewModel: viewModel)
                        .allowsHitTesting(true)
                        .background(Color.clear) // 清除原有背景
                        .overlay(
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(ThemeColors.border(viewModel.currentTheme))
                                .opacity(0.6),
                            alignment: .top
                        )

                    // 添加一个底部安全区域填充
                    Color.clear
                        .frame(height: geometry.safeAreaInsets.bottom)
                }
                .background(ThemeColors.surface(viewModel.currentTheme).opacity(0.95))
                .shadow(color: viewModel.currentTheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
            }
            .edgesIgnoringSafeArea(.bottom)
        }
    }
}

// MARK: - SubtitleAreaView
private struct SubtitleAreaView: View {
    @ObservedObject var viewModel: PlayerViewModel
    @EnvironmentObject var wordSelectionManager: WordSelectionManager
    let showRedownloadButton: Bool

    var body: some View {
        ZStack(alignment: .top) {
            SubtitleListView(
                viewModel: viewModel,
                showRedownloadButton: showRedownloadButton
            )

            // 顶部和底部遮罩
            VStack {
                GradientBlurMask(theme: viewModel.currentTheme)
                    .frame(height: 40)  // 减少遮罩高度从60到40

                Spacer()

                GradientBlurMask(theme: viewModel.currentTheme)
                    .frame(height: 40)  // 减少遮罩高度从60到40
                    .rotation3DEffect(.degrees(180), axis: (x: 1, y: 0, z: 0))
            }
        }
        .allowsHitTesting(wordSelectionManager.selectedWord == nil)
    }
}

// MARK: - SubtitleListView
private struct SubtitleListView: View {
    @ObservedObject var viewModel: PlayerViewModel
    let showRedownloadButton: Bool

    // 添加可视范围追踪
    @State private var visibleSubtitles: Range<Int>? = nil

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 0) {
                    Color.clear.frame(height: 20)  // 减少顶部空间从40到20

                    SubtitleContentView(
                        viewModel: viewModel,
                        showRedownloadButton: showRedownloadButton
                    )
                    .onChange(of: viewModel.shouldScrollToSubtitle) { newValue in
                        if newValue {
                            scrollToCurrentSubtitle(proxy: proxy)
                            // 重置标志
                            viewModel.shouldScrollToSubtitle = false
                        }
                    }

                    Color.clear.frame(height: 40)
                }
                .padding(.horizontal, 16)
                .onAppear {
                    // 如果已经加载完成，直接滚动
                    if viewModel.hasLoaded {
                        scrollToCurrentSubtitle(proxy: proxy)
                    }
                }
                // 移除对 currentTime 的直接监听
                // 改为监听可视范围变化
                .onChange(of: visibleSubtitles) { newValue in
                    if let range = newValue {
                        viewModel.updateVisibleRange(start: range.lowerBound, end: range.upperBound)
                    }
                }
            }
            // 添加 ScrollView 的可视范围追踪
            .onPreferenceChange(ScrollViewOffsetPreferenceKey.self) { bounds in
                updateVisibleSubtitles(bounds)
            }
        }
    }

    private func updateVisibleSubtitles(_ bounds: CGRect) {
        // 计算当前可见的字幕范围
        let visibleHeight = bounds.height
        let itemHeight: CGFloat = 100 // 估计每个字幕项的平均高度
        let visibleItems = Int(visibleHeight / itemHeight)

        // 计算第一个可见项的索引，并确保它是一个可选值
        let firstVisibleIndex = Int(floor(bounds.minY / itemHeight))
        if firstVisibleIndex >= 0 {
            let start = max(0, firstVisibleIndex)
            let end = min(viewModel.subtitles.count, start + visibleItems)
            visibleSubtitles = start..<end
        }
    }

    @MainActor
    private func scrollToCurrentSubtitle(proxy: ScrollViewProxy) {
        if let currentSubtitle = viewModel.getCurrentSubtitle() {
            withAnimation(.interpolatingSpring(
                mass: 1.0,
                stiffness: 100,
                damping: 16,
                initialVelocity: 0
            )) {
                proxy.scrollTo(currentSubtitle.id, anchor: .center)
            }
        }
    }
}

// 添加 ScrollView 偏移量追踪的 PreferenceKey
struct ScrollViewOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGRect = .zero

    static func reduce(value: inout CGRect, nextValue: () -> CGRect) {
        value = nextValue()
    }
}

// 添加 GeometryReader 包装器来追踪 ScrollView 的可视范围
struct ScrollViewOffsetTracking: ViewModifier {
    func body(content: Content) -> some View {
        content.background(GeometryReader { geometry in
            Color.clear.preference(
                key: ScrollViewOffsetPreferenceKey.self,
                value: geometry.frame(in: .global)
            )
        })
    }
}

extension View {
    func trackScrollViewOffset() -> some View {
        modifier(ScrollViewOffsetTracking())
    }
}

// MARK: - SubtitleContentView
private struct SubtitleContentView: View {
    @ObservedObject var viewModel: PlayerViewModel
    let showRedownloadButton: Bool

    var body: some View {
        Group {
            if showRedownloadButton {
                RedownloadButtonView(viewModel: viewModel)
                    .transition(.opacity)
                    .animation(.easeInOut, value: showRedownloadButton)
            } else if viewModel.subtitleDisplayMode == .none {
                NoSubtitlesView(viewModel: viewModel)
            } else {
                ForEach(Array(viewModel.subtitles.enumerated()), id: \.element.id) { index, subtitle in
                    VStack(spacing: 0) {
                        // 字幕项
                        SubtitleItem(
                            subtitle: subtitle,
                            isHighlighted: viewModel.getCurrentSubtitle()?.id == subtitle.id,
                            viewModel: viewModel
                        )
                        .id(subtitle.id)

                        // // 添加分隔符，最后一项不添加
                        // if index < viewModel.subtitles.count - 1 {
                        //     Divider()
                        //         .padding(.horizontal, 16)
                        //         .padding(.vertical, 1)
                        // }
                    }
                }
            }
        }
        .trackScrollViewOffset()
    }
}

// MARK: - RedownloadButtonView
private struct RedownloadButtonView: View {
    @ObservedObject var viewModel: PlayerViewModel
    @StateObject private var downloadViewModel = DownloadViewModel.shared

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 40))
                .foregroundColor(.orange)

            Text("资源文件不完整")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))

            if !downloadViewModel.isDownloading {
                Button(action: {
                    viewModel.redownloadCurrentEpisode()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.clockwise")
                        Text("重新下载")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(ThemeColors.primary)
                    .cornerRadius(8)
                }
            }
        }
        .padding(.vertical, 32)
    }
}

// MARK: - NoSubtitlesView
private struct NoSubtitlesView: View {
    @ObservedObject var viewModel: PlayerViewModel

    var body: some View {
        VStack(spacing: 24) {
            // 图标区域
            ZStack {
                Circle()
                    .fill(ThemeColors.primary.opacity(0.1))
                    .frame(width: 80, height: 80)

                Image(systemName: "text.badge.xmark")
                    .font(.system(size: 32))
                    .foregroundColor(ThemeColors.primary)
            }

            // 文字提示区域
            VStack(spacing: 12) {
                Text("字幕当前已隐藏")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))

                Text("点击底部的语言图标或设置菜单中的字幕语言选项可重新显示")
                    .font(.system(size: 15))
                    .foregroundColor(ThemeColors.secondaryText(viewModel.currentTheme))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 32)
            }

            // 快捷操作按钮
            Button(action: {
                viewModel.setSubtitleDisplayMode(.both)
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "text.bubble")
                        .font(.system(size: 18))
                    Text("显示全部字幕")
                        .font(.system(size: 18))
                }
                .foregroundColor(ThemeColors.primary)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(ThemeColors.primary.opacity(0.1))
                )
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: UIScreen.main.bounds.height * 0.5)
        .padding(.vertical, 24)
    }
}

// MARK: - DictionaryOverlayView
private struct DictionaryOverlayView: View {
    @ObservedObject var wordSelectionManager: WordSelectionManager
    let dictionaryDB: DictionaryDatabase
    let theme: AppTheme
    let viewModel: PlayerViewModel

    var body: some View {
        Group {
            if let (word, frame) = wordSelectionManager.selectedWord,
               let entry = dictionaryDB.lookup(word) {
                GeometryReader { geometry in
                    DictionaryPopover(
                        entry: entry,
                        theme: theme,
                        position: frame,
                        screenHeight: UIScreen.main.bounds.height,
                        seasonNumber: viewModel.currentSeasonNumber,
                        episodeNumber: viewModel.currentEpisodeNumber,
                        episodeTitle: viewModel.currentEpisodeTitle
                    )
                    .zIndex(999)
                    .id(UUID())
                }
                .ignoresSafeArea()
            }
        }
    }
}

// MARK: - DownloadProgressView
struct DownloadProgressView: View {
    let progress: Double
    let currentFile: String

    var body: some View {
        ZStack {
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(.white)

                Text("正在下载\(currentFile)")
                    .foregroundColor(.white)
                    .font(.system(size: 16))

                // 自定义进度条
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 200, height: 8)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.white)
                        .frame(width: 200 * progress, height: 8)
                }

                Text("\(Int(progress * 100))%")
                    .foregroundColor(.white)
                    .font(.system(size: 14))

                // 添加取消按钮
                Button(action: {
                    DownloadViewModel.shared.cancelDownload()
                }) {
                    Text("取消下载")
                        .foregroundColor(.white)
                        .font(.system(size: 15))
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.red.opacity(0.8))
                        )
                }
                .padding(.top, 8)
            }
            .padding(24)
            .background(Color.black.opacity(0.7))
            .cornerRadius(16)
        }
    }
}
