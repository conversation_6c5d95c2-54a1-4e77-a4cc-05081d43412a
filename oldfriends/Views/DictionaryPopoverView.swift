import SwiftUI
import AVFoundation

struct DictionaryPopover: View {
    let entry: DictionaryEntry
    let theme: AppTheme
    let position: CGRect
    let screenHeight: CGFloat
    let seasonNumber: Int?
    let episodeNumber: Int?
    let episodeTitle: String?
    
    // 添加语音合成器
    private let synthesizer = AVSpeechSynthesizer()
    
    // 添加 id 来强制视图刷新
    private let id = UUID()
    
    // 计算弹出框是否应该显示在单词上方
    private var shouldShowAbove: Bool {
        // 如果单词在屏幕下半部分，气泡显示在上方
        return position.midY > screenHeight / 2
    }
    
    // 添加常量定义
    private struct Constants {
        static let width: CGFloat = 300
        static let maxHeight: CGFloat = 280
        static let cornerRadius: CGFloat = 12
        static let shadowRadius: CGFloat = 12
        static let arrowWidth: CGFloat = 14
        static let arrowHeight: CGFloat = 8
        static let wordMaxWidth: CGFloat = width * 0.7 // 调整单词最大宽度为气泡宽度的70%
        static let buttonSize: CGFloat = 32
    }
    
    // 新颜色方案
    private struct PopoverColors {
        static func background(_ theme: AppTheme) -> Color {
            theme == .dark ? Color(hex: "#2C2C2E") : .white
        }
        
        static func primaryText(_ theme: AppTheme) -> Color {
            theme == .dark ? .white : Color(hex: "#1C1C1E")
        }
        
        static func secondaryText(_ theme: AppTheme) -> Color {
            theme == .dark ? Color(hex: "#EBEBF5").opacity(0.6) : Color(hex: "#3C3C43").opacity(0.6)
        }
        
        static func divider(_ theme: AppTheme) -> Color {
            theme == .dark ? Color.white.opacity(0.2) : Color.black.opacity(0.1)
        }
        
        static func border(_ theme: AppTheme) -> Color {
            theme == .dark ? Color.white.opacity(0.1) : Color.black.opacity(0.05)
        }
        
        static func accent(_ theme: AppTheme) -> Color {
            theme == .dark ? Color(hex: "#0A84FF") : Color(hex: "#007AFF")
        }
    }
    
    @EnvironmentObject var favoriteManager: FavoriteManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 单词、音标和发音按钮部分
            VStack(alignment: .leading, spacing: 8) {
                HStack(alignment: .top, spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        // 单词显示区域
                        Text(entry.word)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(PopoverColors.primaryText(theme))
                            .lineLimit(2)
                            .fixedSize(horizontal: false, vertical: true)
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: Constants.wordMaxWidth, alignment: .leading)
                        
                        if let phonetic = entry.phonetic {
                            Text("/\(phonetic)/")
                                .font(.system(size: 14, weight: .regular))
                                .foregroundColor(PopoverColors.secondaryText(theme))
                                .lineLimit(1)
                        }
                    }
                    
                    Spacer()
                    
                    // 发音和收藏按钮
                    HStack(spacing: 8) {
                        Button(action: {
                            speakWord(entry.word)
                        }) {
                            Image(systemName: "speaker.wave.2.fill")
                                .font(.system(size: 16))
                                .foregroundColor(PopoverColors.accent(theme))
                                .padding(6)
                                .background(
                                    Circle()
                                        .fill(PopoverColors.accent(theme).opacity(0.1))
                                )
                        }
                        
                        Button(action: {
                            favoriteManager.toggleWordFavorite(
                                entry.word,
                                seasonNumber: seasonNumber,
                                episodeNumber: episodeNumber,
                                episodeTitle: episodeTitle
                            )
                        }) {
                            Image(systemName: favoriteManager.isWordFavorited(entry.word) ? "star.fill" : "star")
                                .font(.system(size: 16))
                                .foregroundColor(favoriteManager.isWordFavorited(entry.word) ? .yellow : PopoverColors.secondaryText(theme))
                        }
                    }
                }
            }
            
            // 分割线
            Divider()
                .background(PopoverColors.divider(theme))
            
            // 释义部分
            ScrollView(showsIndicators: false) {
                VStack(alignment: .leading, spacing: 12) {
                    if let translation = entry.translation {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("中文释义")
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(PopoverColors.secondaryText(theme))
                            
                            // 将翻译文本按换行符分割并显示
                            ForEach(translation.components(separatedBy: "\\n"), id: \.self) { line in
                                Text(line)
                                    .font(.system(size: 14))
                                    .foregroundColor(PopoverColors.primaryText(theme))
                                    .lineSpacing(4)
                            }
                        }
                    }
                    
                    if let definition = entry.definition {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("英文释义")
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(PopoverColors.secondaryText(theme))
                            
                            // 将英文释义按换行符分割并显示
                            ForEach(definition.components(separatedBy: "\\n"), id: \.self) { line in
                                Text(line)
                                    .font(.system(size: 14))
                                    .foregroundColor(PopoverColors.primaryText(theme))
                                    .lineSpacing(4)
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            .frame(maxHeight: Constants.maxHeight - 100)
        }
        .padding(.horizontal, 18)
        .padding(.vertical, 16)
        .background {
            ZStack {
                // 主背景
                RoundedRectangle(cornerRadius: Constants.cornerRadius)
                    .fill(PopoverColors.background(theme))
                    .overlay(
                        RoundedRectangle(cornerRadius: Constants.cornerRadius)
                            .stroke(PopoverColors.border(theme), lineWidth: 1)
                    )
                
                // 箭头
                GeometryReader { geometry in
                    Path { path in
                        let arrowX = calculateArrowX(geometry: geometry, position: position)
                        
                        if shouldShowAbove {
                            // 向下的箭头
                            path.move(to: CGPoint(x: arrowX - Constants.arrowWidth/2, y: geometry.size.height))
                            path.addLine(to: CGPoint(x: arrowX + Constants.arrowWidth/2, y: geometry.size.height))
                            path.addLine(to: CGPoint(x: arrowX, y: geometry.size.height + Constants.arrowHeight))
                        } else {
                            // 向上的箭头
                            path.move(to: CGPoint(x: arrowX - Constants.arrowWidth/2, y: 0))
                            path.addLine(to: CGPoint(x: arrowX + Constants.arrowWidth/2, y: 0))
                            path.addLine(to: CGPoint(x: arrowX, y: -Constants.arrowHeight))
                        }
                    }
                    .fill(PopoverColors.background(theme))
                }
            }
        }
        .frame(width: Constants.width)
        .overlay(
            RoundedRectangle(cornerRadius: Constants.cornerRadius)
                .stroke(PopoverColors.border(theme), lineWidth: 1)
        )
        .offset(x: calculateXOffset(position: position), y: calculateYOffset(position: position))
        .shadow(
            color: Color.black.opacity(theme == .dark ? 0.3 : 0.1),
            radius: Constants.shadowRadius,
            x: 0,
            y: 4
        )
        .onAppear {
//            let xOffset = calculateXOffset(position: position)
//            let yOffset = calculateYOffset(position: position)
//            // print("""
//            ==================
//            气泡位置更新(DictionaryPopover.onAppear):
//            - 词: \(entry.word)
//            - 输入位置: \(position)
//            - X轴偏移: \(xOffset)
//            - Y轴偏移: \(yOffset)
//            - 是否显示在上方: \(shouldShowAbove)
//            ==================
//            """)
        }
        .onChange(of: position) { newPosition in
//            let xOffset = calculateXOffset(position: newPosition)
//            let yOffset = calculateYOffset(position: newPosition)
//            // print("""
//            ==================
//            气泡位置更新:
//            - 单词: \(entry.word)
//            - 输入位置: \(newPosition)
//            - X轴偏移: \(xOffset)
//            - Y轴偏移: \(yOffset)
//            - 是否显示在上方: \(shouldShowAbove)
//            ==================
//            """)
        }
        // 添加 id 强制视图刷新
        .id(id)
    }
    
    // 计算箭头X位置
    private func calculateArrowX(geometry: GeometryProxy, position: CGRect) -> CGFloat {
        let idealX = position.midX - calculateXOffset(position: position)
        let minX = Constants.cornerRadius + Constants.arrowWidth/2
        let maxX = geometry.size.width - Constants.cornerRadius - Constants.arrowWidth/2
        return min(max(idealX, minX), maxX)
    }
    
    // 修改X轴偏移计算
    private func calculateXOffset(position: CGRect) -> CGFloat {
        // 计算理想的X轴中心位置
        let idealXPosition = position.midX - Constants.width/2
        
        // 确保不超出屏幕边界
        let minX: CGFloat = 16  // 左边界最小间距
        let maxX = UIScreen.main.bounds.width - Constants.width - 16  // 右边界最大位置
        
        return min(max(idealXPosition, minX), maxX)
    }
    
    // 修改Y轴偏移计算
    private func calculateYOffset(position: CGRect) -> CGFloat {
        let spacing: CGFloat = 2  // 保持小间距
        
        if shouldShowAbove {
            // 显示在单词上方时，向上偏移气泡高度和箭头高度
            return -(Constants.maxHeight + Constants.arrowHeight + spacing) + position.minY
        } else {
            // 显示在单词下方时，向下偏移
            return position.maxY + Constants.arrowHeight + spacing
        }
    }
    
    // 添加发音方法
    private func speakWord(_ word: String) {
        let utterance = AVSpeechUtterance(string: word)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0
        
        synthesizer.stopSpeaking(at: .immediate)
        synthesizer.speak(utterance)
    }
}

// 预览
struct DictionaryPopover_Previews: PreviewProvider {
    static var previews: some View {
        DictionaryPopover(
            entry: DictionaryEntry(
                word: "test",
                translation: "测试",
                phonetic: "test",
                definition: "To test something",
                exchange: nil
            ),
            theme: .dark,
            position: CGRect(x: 0, y: 0, width: 0, height: 0),
            screenHeight: UIScreen.main.bounds.height,
            seasonNumber: 1,
            episodeNumber: 1,
            episodeTitle: "Episode Title"
        )
    }
} 
