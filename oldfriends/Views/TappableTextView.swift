import SwiftUI
import UIKit

struct TappableTextView: UIViewRepresentable {
    let text: String
    let onWordTapped: (String, CGRect) -> Void
    let onWordLongPressed: ((String, CGRect) -> Void)?
    let isHighlighted: Bool
    let theme: AppTheme
    let selectedWord: String?
    @ObservedObject var wordSelectionManager: WordSelectionManager

    init(
        text: String,
        onWordTapped: @escaping (String, CGRect) -> Void,
        onWordLongPressed: ((String, CGRect) -> Void)? = nil,
        isHighlighted: Bool,
        theme: AppTheme,
        selectedWord: String?,
        wordSelectionManager: WordSelectionManager
    ) {
        self.text = text
        self.onWordTapped = onWordTapped
        self.onWordLongPressed = onWordLongPressed
        self.isHighlighted = isHighlighted
        self.theme = theme
        self.selectedWord = selectedWord
        self.wordSelectionManager = wordSelectionManager
    }

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.isEditable = false
        textView.isSelectable = false
        textView.backgroundColor = .clear
        textView.textAlignment = .left
        textView.isScrollEnabled = false

        // 修改自动布局配置
        textView.translatesAutoresizingMaskIntoConstraints = false

        // 配置文本容器
        textView.textContainer.lineBreakMode = .byWordWrapping
        textView.textContainer.maximumNumberOfLines = 0
        textView.textContainer.lineFragmentPadding = 0

        // 设置内边距 - 移除左右内边距，确保与中文字幕对齐
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)

        // 设置固定宽度约束，确保文本有足够空间换行
        textView.widthAnchor.constraint(equalToConstant: UIScreen.main.bounds.width - 32).isActive = true

        // 设置最小高度约束
        textView.heightAnchor.constraint(greaterThanOrEqualToConstant: 44).isActive = true

        // 添加长按手势
        let tapGesture = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleTap(_:)))
        textView.addGestureRecognizer(tapGesture)

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        // 创建富文本
        let attributedString = NSMutableAttributedString(string: text)

        // 设置段落样式
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .left
        paragraphStyle.lineBreakMode = .byWordWrapping
        paragraphStyle.lineSpacing = 4

        // 基本文本属性 - 提升英文字幕的视觉重要性
        let baseAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor(isHighlighted ?
                ThemeColors.primary :
                ThemeColors.primaryText(theme)), // 使用主要文本色，突出英文重要性
            .font: UIFont.systemFont(ofSize: 16, weight: .medium), // 增大字号并使用中等字重
            .paragraphStyle: paragraphStyle
        ]

        attributedString.addAttributes(baseAttributes, range: NSRange(location: 0, length: text.count))

        // 设置文本
        uiView.attributedText = attributedString

        // 强制布局更新
        uiView.setNeedsLayout()
        uiView.layoutIfNeeded()

        // 确保宽度设置正确 - 使用屏幕宽度减去边距
        let screenWidth = UIScreen.main.bounds.width
        uiView.frame.size.width = screenWidth - 32

        // 调整高度以适应内容
        let size = uiView.sizeThatFits(CGSize(width: uiView.frame.width, height: CGFloat.greatestFiniteMagnitude))
        if size.height > 0 {
            uiView.frame.size.height = size.height
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(text: text, onWordTapped: onWordTapped, onWordLongPressed: onWordLongPressed, wordSelectionManager: wordSelectionManager)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        let text: String
        let onWordTapped: (String, CGRect) -> Void
        let onWordLongPressed: ((String, CGRect) -> Void)?
        let wordSelectionManager: WordSelectionManager
        var lastTappedRange: NSRange?
        weak var textView: UITextView?

        init(text: String,
             onWordTapped: @escaping (String, CGRect) -> Void,
             onWordLongPressed: ((String, CGRect) -> Void)? = nil,
             wordSelectionManager: WordSelectionManager) {
            self.text = text
            self.onWordTapped = onWordTapped
            self.onWordLongPressed = onWordLongPressed
            self.wordSelectionManager = wordSelectionManager
            super.init()
        }

        // 添加点击处理方法
         @objc func handleTap(_ gesture: UITapGestureRecognizer) {


            guard let textView = gesture.view as? UITextView else { return }
            self.textView = textView
            let location = gesture.location(in: textView)

            // print("开始处理点击事件")

            if let position = textView.closestPosition(to: location),
               let wordRange = textView.tokenizer.rangeEnclosingPosition(position, with: .word, inDirection: .layout(.left)) {
                let word = textView.text(in: wordRange) ?? ""
                // print("检测到点击的原始单词: \(word)")

                // 获取单词在 textView 中的位置
                let rect = textView.firstRect(for: wordRange)
                let globalRect = textView.convert(rect, to: nil)

                // 正确转换 UITextRange 到 NSRange
                let start = textView.offset(from: textView.beginningOfDocument, to: wordRange.start)
                let length = textView.offset(from: wordRange.start, to: wordRange.end)
                lastTappedRange = NSRange(location: start, length: length)

                // 清理单词，保留撇号
                let cleanWord = word.trimmingCharacters(in: CharacterSet.punctuationCharacters.subtracting(CharacterSet(charactersIn: "'")))

                // 验证是否为有效的英文单词或缩写
                if isValidEnglishWordOrContraction(cleanWord) {
                    // 提取原型单词
                    let baseWord = extractBaseWord(cleanWord)
                    // print("有效的英文单词，原型: \(baseWord), 显示形式: \(cleanWord)")
                    // 传递原型和显示形式
                    onWordTapped(baseWord + "|" + cleanWord, globalRect)
                } else {
                    // print("无效的英文单词或缩写，不触发回调")
                }
            } else {
                // print("未能检测到有效的单词范围")
                // 点击到非单词区域时，传递特殊值来关闭气泡
                wordSelectionManager.selectedWord = nil
            }
        }

        // 添加提取原型单词的方法
        private func extractBaseWord(_ word: String) -> String {
            if word.contains("'") {
                if word.hasSuffix("'s") {
                    return String(word.dropLast(2))
                } else if word.hasSuffix("'t") ||
                          word.hasSuffix("'m") ||
                          word.hasSuffix("'d") {
                    return String(word.dropLast(2))
                } else if word.hasSuffix("'ve") ||
                          word.hasSuffix("'ll") ||
                          word.hasSuffix("'re") {
                    return String(word.dropLast(3))
                }
            }
            return word
        }

        // 新增：验证英文单词或缩写的方法
        private func isValidEnglishWordOrContraction(_ word: String) -> Bool {
            // 允许包含字母和撇号的正则表达式
            let pattern = "^[a-zA-Z]+('[a-zA-Z]+)?$"
            let regex = try? NSRegularExpression(pattern: pattern)
            let range = NSRange(word.startIndex..., in: word)

            let isValid = regex?.firstMatch(in: word, range: range) != nil
            // print("单词 '\(word)' 验证结果: \(isValid)")
            return isValid
        }
    }
}
