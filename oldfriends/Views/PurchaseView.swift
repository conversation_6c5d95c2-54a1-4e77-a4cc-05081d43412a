/*
 * 文件名：PurchaseView.swift
 * 功能：会员购买页面
 * 上游依赖：
 *   - MembershipManager：会员管理服务
 *   - StoreHelper：应用内购买管理
 *   - PlayerViewModel：主题管理
 * 下游调用：
 *   - HomeView：主页面
 *   - ProfileView：个人资料页面
 */

import SwiftUI
import StoreKit
import StoreHelper
import os.log

// MARK: - PurchaseState
enum PurchaseState {
    case notStarted, pending, purchased, failed, cancelled, unknown, notPurchased
    case userCannotMakePayments, inProgress, failedVerification
}

// MARK: - PurchaseView
struct PurchaseView: View {
    // MARK: - Properties
    @Environment(\.dismiss) var dismiss
    @Environment(\.isStoreReady) private var isStoreReady
    @EnvironmentObject var storeHelper: StoreHelper
    @EnvironmentObject var viewModel: PlayerViewModel
    @StateObject private var membershipManager = MembershipManager.shared
    @State private var purchaseState: PurchaseState = .unknown
    @State private var hasCheckedPurchase = false
    @State private var isRestoringPurchase = false
    @State private var showNetworkAlert = false

    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "PurchaseView")
    private let productId = Constants.Store.premiumProductId

    let theme: AppTheme

    // MARK: - Body
    var body: some View {
        MainPurchaseView(
            theme: theme,
            purchaseState: $purchaseState,
            isRestoringPurchase: $isRestoringPurchase,
            showNetworkAlert: $showNetworkAlert,
            productId: productId,
            restorePurchases: restorePurchases
        )
        .onAppear {
            logger.info("购买页面显示，当前主题：\(theme == .dark ? "深色" : "浅色")")
            // 重置状态
            if !membershipManager.isFromShowDrawer {
                logger.info("重置 isFromShowDrawer 状态")
                membershipManager.isFromShowDrawer = false
            }
        }
        .task {
            guard !hasCheckedPurchase else { return }
            hasCheckedPurchase = true

            if await membershipManager.canAccessPremiumFeature() {
                await MainActor.run {
                    // 如果是从剧集选择页面触发的购买，则返回到剧集选择页面
                    if membershipManager.isFromShowDrawer {
                        logger.info("用户已是会员，从剧集选择页面触发的购买，准备返回剧集页面")
                        membershipManager.isFromShowDrawer = false

                        dismiss()

                        // 延迟一帧再发送通知，确保购买页面已完全关闭
                        DispatchQueue.main.async {
                            NotificationCenter.default.post(
                                name: NSNotification.Name("ShowEpisodeSelection"),
                                object: nil
                            )
                        }
                    } else {
                        dismiss()
                    }
                }
            } else {
                purchaseState = .notPurchased
            }
        }
        .onChange(of: storeHelper.purchasedProducts) { newValue in
            Task {
                if await membershipManager.canAccessPremiumFeature() {
                    await MainActor.run {
                        dismiss()

                        // 如果是从剧集选择页面触发的购买，则返回到剧集选择页面
                        if membershipManager.isFromShowDrawer {
                            logger.info("购买状态变化，从剧集选择页面触发的购买，准备返回剧集页面")
                            membershipManager.isFromShowDrawer = false

                            // 延迟一帧再发送通知，确保购买页面已完全关闭
                            DispatchQueue.main.async {
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("ShowEpisodeSelection"),
                                    object: nil
                                )
                            }
                        }

                        NotificationCenter.default.post(
                            name: NSNotification.Name("ShowSuccessToast"),
                            object: nil,
                            userInfo: ["message": "购买成功！已解锁所有高级功能"]
                        )
                    }
                }
            }
        }
    }

    // MARK: - MainPurchaseView
    private struct MainPurchaseView: View {
        let theme: AppTheme
        @Environment(\.dismiss) var dismiss
        @Environment(\.isStoreReady) private var isStoreReady
        @Binding var purchaseState: PurchaseState
        @Binding var isRestoringPurchase: Bool
        @Binding var showNetworkAlert: Bool
        let productId: String
        let restorePurchases: () -> Void

        @EnvironmentObject var storeHelper: StoreHelper
        @StateObject private var membershipManager = MembershipManager.shared
        private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "MainPurchaseView")

        var body: some View {
            ZStack(alignment: .top) {
                // 背景色
                ThemeColors.background(theme)
                    .edgesIgnoringSafeArea(.all)
                    .onAppear {
                        logger.info("MainPurchaseView 显示，当前主题：\(theme == .dark ? "深色" : "浅色")")
                    }

                // 内容
                VStack(spacing: 0) {
                    // 顶部拖动指示器
                    DragIndicator(theme: theme)

                    if !isStoreReady {
                        // 网络连接错误视图
                        NetworkErrorView(theme: theme, showNetworkAlert: $showNetworkAlert, dismiss: dismiss)
                    } else {
                        // 主要内容
                        ScrollView {
                            VStack(spacing: 14) {
                                // 标题部分
                                HeaderSection(theme: theme)

                                // 价格部分
                                PriceSection(theme: theme, productId: productId)

                                // 功能列表
                                FeaturesSection(theme: theme)

                                // 购买状态
                                if purchaseState != .unknown && purchaseState != .notPurchased {
                                    PurchaseStatusView(theme: theme, purchaseState: purchaseState)
                                        .padding(.top, 4)
                                }

                                Spacer(minLength: 4)

                                // 购买按钮
                                if let product = storeHelper.product(from: productId) {
                                    PurchaseButtonView(
                                        theme: theme,
                                        product: product,
                                        purchaseState: $purchaseState,
                                        isRestoringPurchase: $isRestoringPurchase
                                    )
                                    .padding(.horizontal)
                                    .padding(.top, -4)
                                } else {
                                    Text("产品加载中...")
                                        .font(.headline)
                                        .foregroundColor(ThemeColors.secondaryText(theme))
                                        .padding()
                                }

                                // 底部说明文字
                                FooterSection(
                                    theme: theme,
                                    isRestoringPurchase: $isRestoringPurchase,
                                    onRestorePurchase: restorePurchases
                                )
                            }
                            .padding(.horizontal, 16)
                            .padding(.bottom, 8)
                        }
                        .padding(.top, 0)
                        .background(ThemeColors.surface(theme).opacity(0.95))
                    }
                }
                .background(
                    ThemeColors.surface(theme)
                        .opacity(0.98)
                        .shadow(
                            color: theme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1),
                            radius: 3,
                            x: 0,
                            y: 2
                        )
                )
            }
            .presentationDetents([.height(500)])
            .presentationDragIndicator(.hidden)
            .disabled(purchaseState == .pending || isRestoringPurchase)
            .overlay(
                Group {
                    if purchaseState == .pending || isRestoringPurchase {
                        ProgressView()
                            .scaleEffect(1.5)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(Color.black.opacity(0.2))
                    }
                }
            )
            .alert("需要网络访问权限", isPresented: $showNetworkAlert) {
                Button("去设置") {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("听商务英语 需要网络访问权限来处理购买。请在设置中允许网络访问。")
            }
        }
    }

    // MARK: - NetworkErrorView
    private struct NetworkErrorView: View {
        let theme: AppTheme
        @Binding var showNetworkAlert: Bool
        var dismiss: DismissAction

        var body: some View {
            VStack(spacing: 16) {
                Image(systemName: "wifi.slash")
                    .font(.system(size: 40))
                    .foregroundColor(ThemeColors.secondaryText(theme))

                Text("需要网络连接")
                    .font(.headline)
                    .foregroundColor(ThemeColors.primaryText(theme))

                Text("请检查网络连接并授权网络访问权限")
                    .font(.subheadline)
                    .foregroundColor(ThemeColors.secondaryText(theme))
                    .multilineTextAlignment(.center)

                Button(action: {
                    showNetworkAlert = true
                }) {
                    Text("去设置")
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(ThemeColors.primary)
                        .cornerRadius(8)
                }
                .padding(.top, 8)

                // 取消按钮
                Button(action: {
                    AnalyticsManager.shared.logCancelPurchaseClick()
                    dismiss()
                }) {
                    Text("暂不购买")
                        .font(.system(size: 14))
                        .foregroundColor(ThemeColors.secondaryText(theme))
                }
                .padding(.top, 12)
            }
            .padding()
        }
    }

    // MARK: - DragIndicator
    private struct DragIndicator: View {
        let theme: AppTheme

        var body: some View {
            RoundedRectangle(cornerRadius: 2.5)
                .fill(ThemeColors.secondaryText(theme).opacity(0.3))
                .frame(width: 36, height: 5)
                .padding(.top, 6)
                .padding(.bottom, 10)
        }
    }

    // MARK: - HeaderSection
    private struct HeaderSection: View {
        let theme: AppTheme

        var body: some View {
            HStack(spacing: 12) {
                // App 图标
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    ThemeColors.primary.opacity(0.2),
                                    ThemeColors.primary.opacity(0.1)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(width: 44, height: 44)  // 调整图标大小
                        .shadow(
                            color: ThemeColors.primary.opacity(0.2),
                            radius: 4,
                            x: 0,
                            y: 2
                        )

                    Image("AppIcon")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 32, height: 32)  // 调整图标大小
                        .clipShape(Circle())
                }

                // 文本部分
                VStack(alignment: .leading, spacing: 4) {
                    Text("和老友们一起学英语吧！")
                        .font(.title3)
                        .bold()
                        .foregroundColor(ThemeColors.primaryText(theme))

                    Text("生活场景化剧情，边听边学超实用～")
                        .font(.subheadline)
                        .foregroundColor(ThemeColors.secondaryText(theme))
                }

                Spacer()
            }
            .padding(.horizontal, 4)
            .padding(.bottom, 8)
        }
    }

    // MARK: - PriceSection
    private struct PriceSection: View {
        let theme: AppTheme
        let productId: String
        @EnvironmentObject var storeHelper: StoreHelper

        var body: some View {
            VStack(spacing: 2) {
                if let product = storeHelper.product(from: productId) {
                    HStack(alignment: .firstTextBaseline, spacing: 4) {

                        Text(product.displayPrice)
                            .font(.system(size: 40, weight: .bold))
                            .foregroundColor(ThemeColors.primaryText(theme))
                        Text(product.price * 8, format: .currency(code: product.priceFormatStyle.currencyCode))
                            .font(.system(size: 16))
                            .foregroundColor(ThemeColors.secondaryText(theme))
                            .strikethrough(true, color: ThemeColors.secondaryText(theme))
                        Text("/永久")
                            .font(.subheadline)
                            .foregroundColor(ThemeColors.secondaryText(theme))
                    }

                    HStack(spacing: 8) {
                        PriceTag(text: "限时特惠", color: .pink)
                        PriceTag(text: "终身会员", color: ThemeColors.primary)
                    }
                }
            }
            .padding(.bottom, 12)
        }
    }

    // MARK: - PriceTag
    private struct PriceTag: View {
        let text: String
        let color: Color

        var body: some View {
            Text(text)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(color)
                .cornerRadius(4)
        }
    }

    // MARK: - FeaturesSection
    private struct FeaturesSection: View {
        let theme: AppTheme

        var body: some View {
            VStack(spacing: 6) {
                Text("会员专属特权")
                    .font(.headline)
                    .foregroundColor(ThemeColors.primaryText(theme))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.bottom, 2)

                FeatureRow(
                    theme: theme,
                    icon: "play.circle.fill",
                    title: "一次解锁 终身畅学"
                )

                FeatureRow(
                    theme: theme,
                    icon: "arrow.down.circle.fill",
                    title: "支持离线 随时学习"
                )

                FeatureRow(
                    theme: theme,
                    icon: "doc.fill",
                    title: "导出资料 永久留存"
                )

                FeatureRow(
                    theme: theme,
                    icon: "star.fill",
                    title: "持续更新 永久免费"
                )
            }
        }
    }

    // MARK: - FeatureRow
    private struct FeatureRow: View {
        let theme: AppTheme
        let icon: String
        let title: String

        var body: some View {
            HStack(alignment: .center, spacing: 10) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    ThemeColors.primary.opacity(0.15),
                                    ThemeColors.primary.opacity(0.05)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(width: 32, height: 32)
                        .shadow(
                            color: ThemeColors.primary.opacity(0.1),
                            radius: 2,
                            x: 0,
                            y: 1
                        )

                    Image(systemName: icon)
                        .font(.system(size: 14))
                        .foregroundColor(ThemeColors.primary)
                }

                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(ThemeColors.primaryText(theme))

                Spacer()
            }
            .padding(.vertical, 2)
        }
    }

    // MARK: - PurchaseStatusView
    private struct PurchaseStatusView: View {
        let theme: AppTheme
        let purchaseState: PurchaseState

        var body: some View {
            switch purchaseState {
            case .purchased:
                Text("欢迎加入老友圈！🎉")
                    .font(.headline)
                    .foregroundColor(ThemeColors.success)
            case .pending:
                Text("正在为你开启学习之旅...")
                    .font(.headline)
                    .foregroundColor(ThemeColors.primary)
            case .notStarted:
                Text("准备好和老友们学英语了吗？")
                    .font(.headline)
                    .foregroundColor(ThemeColors.secondaryText(theme))
            case .userCannotMakePayments:
                Text("暂时无法完成购买哦")
                    .font(.headline)
                    .foregroundColor(ThemeColors.error)
            case .inProgress:
                Text("正在处理中，请稍等...")
                    .font(.headline)
                    .foregroundColor(ThemeColors.info)
            case .cancelled:
                Text("下次再一起学习吧~")
                    .font(.headline)
                    .foregroundColor(ThemeColors.error)
            case .failed:
                Text("出了点小问题，再试一次？")
                    .font(.headline)
                    .foregroundColor(ThemeColors.error)
            case .failedVerification:
                Text("验证遇到问题，请重试")
                    .font(.headline)
                    .foregroundColor(ThemeColors.error)
            case .unknown:
                Text("未知状态")
                    .font(.headline)
                    .foregroundColor(ThemeColors.secondaryText(theme))
            case .notPurchased:
                Text("未购买")
                    .font(.headline)
                    .foregroundColor(ThemeColors.secondaryText(theme))
            }
        }
    }

    // MARK: - PurchaseButtonView
    private struct PurchaseButtonView: View {
        let theme: AppTheme
        let product: Product
        @Binding var purchaseState: PurchaseState
        @Binding var isRestoringPurchase: Bool
        @EnvironmentObject var storeHelper: StoreHelper
        @StateObject private var membershipManager = MembershipManager.shared
        @Environment(\.dismiss) var dismiss
        private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "PurchaseButtonView")

        var body: some View {
            Button(action: {
                // 添加购买按钮点击埋点
                AnalyticsManager.shared.logPurchaseButtonClick()

                Task {
                    do {
                        purchaseState = .pending
                        logger.info("开始购买流程: \(product.id)")

                        let result = try await storeHelper.purchase(product)
                        if let transaction = result.transaction {
                            await transaction.finish()
                            purchaseState = .purchased
                            await membershipManager.handlePurchase()

                            logger.info("购买成功: \(product.id)")

                            // 购买成功后关闭当前视图
                            await MainActor.run {
                                dismiss()

                                // 发送购买成功通知，显示提示信息
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("ShowSuccessToast"),
                                    object: nil,
                                    userInfo: ["message": "购买成功！已解锁所有高级功能"]
                                )

                                // 如果是从剧集选择页面触发的购买，则返回到剧集选择页面
                                if membershipManager.isFromShowDrawer {
                                    logger.info("从剧集选择页面触发的购买，准备返回剧集页面")
                                    membershipManager.isFromShowDrawer = false

                                    // 延迟一帧再发送通知，确保购买页面已完全关闭
                                    DispatchQueue.main.async {
                                        NotificationCenter.default.post(
                                            name: NSNotification.Name("ShowEpisodeSelection"),
                                            object: nil
                                        )
                                    }
                                }

                                // 同时发送原有的通知
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("PurchaseSuccessful"),
                                    object: nil
                                )
                            }
                        } else {
                            logger.warning("购买失败: 无效交易")
                            purchaseState = .failed
                        }
                    } catch {
                        logger.error("购买过程中发生错误: \(error.localizedDescription)")
                        purchaseState = .failed
                    }
                }
            }) {
                HStack {
                    Text("加入老友圈")
                        .fontWeight(.semibold)
                    Text(product.displayPrice)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 46)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            ThemeColors.primary,
                            ThemeColors.primary.opacity(0.9)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .foregroundColor(.white)
                .cornerRadius(10)
                .shadow(
                    color: ThemeColors.primary.opacity(0.3),
                    radius: 4,
                    x: 0,
                    y: 2
                )
            }
            .disabled(purchaseState == .pending || isRestoringPurchase)
        }
    }

    // MARK: - FooterSection
    private struct FooterSection: View {
        let theme: AppTheme
        @Binding var isRestoringPurchase: Bool
        let onRestorePurchase: () -> Void
        @Environment(\.dismiss) var dismiss

        var body: some View {
            VStack(spacing: 4) {
                Text("首次开通后需要耐心等待一段时间")
                    .font(.caption2)
                    .foregroundColor(ThemeColors.secondaryText(theme))

                HStack(spacing: 4) {
                    if isRestoringPurchase {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.7)
                        Text("恢复中...")
                            .font(.caption2)
                            .foregroundColor(ThemeColors.info)
                    } else {
                        Text("如果您已开通过，可点击")
                            .font(.caption2)
                            .foregroundColor(ThemeColors.secondaryText(theme))

                        Button(action: {
                            guard !isRestoringPurchase else { return }
                            onRestorePurchase()
                        }) {
                            Text("恢复购买")
                                .font(.caption2)
                                .foregroundColor(ThemeColors.info)
                                .bold()
                        }
                        .disabled(isRestoringPurchase)
                    }
                }

                Button(action: {
                    AnalyticsManager.shared.logCancelPurchaseClick()
                    dismiss()
                }) {
                    Text("暂不购买")
                        .font(.system(size: 13))
                        .foregroundColor(ThemeColors.secondaryText(theme))
                }
                .padding(.top, 4)
            }
            .padding(.vertical, 8)
        }
    }

    // MARK: - Methods
    private func restorePurchases() {
        Task {
            do {
                // 开始恢复购买流程
                logger.info("开始恢复购买流程")
                isRestoringPurchase = true

                // 使用 StoreKit 的标准恢复购买方法
                var foundPurchase = false

                for await result in Transaction.currentEntitlements {
                    do {
                        // 这里可能会抛出错误
                        let transaction = try result.payloadValue

                        // 验证是否是我们的产品
                        if transaction.productID == productId {
                            try await transaction.finish()
                            await membershipManager.handlePurchase()
                            logger.info("成功恢复购买")
                            foundPurchase = true

                            await MainActor.run {
                                isRestoringPurchase = false
                                purchaseState = .purchased
                                dismiss()

                                // 显示恢复购买成功的提示
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("ShowSuccessToast"),
                                    object: nil,
                                    userInfo: ["message": "恢复购买成功！已解锁所有高级功能"]
                                )

                                // 如果是从剧集选择页面触发的恢复购买，则返回到剧集选择页面
                                if membershipManager.isFromShowDrawer {
                                    logger.info("从剧集选择页面触发的恢复购买，准备返回剧集页面")
                                    membershipManager.isFromShowDrawer = false

                                    // 延迟一帧再发送通知，确保购买页面已完全关闭
                                    DispatchQueue.main.async {
                                        NotificationCenter.default.post(
                                            name: NSNotification.Name("ShowEpisodeSelection"),
                                            object: nil
                                        )
                                    }
                                }
                            }
                            return
                        }
                    } catch {
                        logger.error("处理交易时出错: \(error.localizedDescription)")
                        throw error // 重新抛出错误以便外层 catch 块捕获
                    }
                }

                // 如果没有找到可恢复的购买
                if !foundPurchase {
                    logger.info("没有找到可恢复的购买")
                    await MainActor.run {
                        isRestoringPurchase = false
                        purchaseState = .notPurchased
                        NotificationCenter.default.post(
                            name: NSNotification.Name("ShowErrorToast"),
                            object: nil,
                            userInfo: ["message": "未找到可恢复的购买记录"]
                        )
                    }
                }
            } catch {
                logger.error("恢复购买失败: \(error.localizedDescription)")
                await MainActor.run {
                    isRestoringPurchase = false
                    purchaseState = .failed
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ShowErrorToast"),
                        object: nil,
                        userInfo: ["message": "恢复购买失败，请稍后重试"]
                    )
                }
            }
        }
    }
}
