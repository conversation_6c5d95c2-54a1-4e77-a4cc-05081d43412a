//
//  SpeedOptionsPopover.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - SpeedOptionsPopover
struct SpeedOptionsPopover: View {
    // MARK: - Properties
    let currentSpeed: Double
    let theme: AppTheme
    let onSpeedSelected: (Double) -> Void
    
    private let speedOptions: [(label: String, value: Double, icon: String)] = [
        ("0.5x 减速", 0.5, "tortoise.fill"),
        ("0.75x 慢速", 0.75, "hare.fill"),
        ("1.0x 正常", 1.0, "figure.walk"),
        ("1.25x 快速", 1.25, "figure.run"),
        ("1.5x 加速", 1.5, "bolt.fill"),
        ("2.0x 最快", 2.0, "bolt.horizontal.fill")
    ]
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            ForEach(speedOptions, id: \.value) { option in
                Button(action: {
                    // print("[SpeedOptionsPopover] 用户选择了: \(option.label)")
                    onSpeedSelected(option.value)
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: option.icon)
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.primary)
                            .frame(width: 16)
                        
                        Text(option.label)
                            .font(.system(size: 15))
                            .foregroundColor(ThemeColors.primaryText(theme))
                        
                        Spacer()
                        
                        if option.value == currentSpeed {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12))
                                .foregroundColor(ThemeColors.primary)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 12)
                    .contentShape(Rectangle())
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.clear))
                
                if option.value != speedOptions.last?.value {
                    Divider()
                        .padding(.horizontal, 12)
                }
            }
        }
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(ThemeColors.background(theme))
                .shadow(color: Color.black.opacity(0.15), radius: 10, x: 0, y: 4)
        )
        .frame(width: 140)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(ThemeColors.border(theme), lineWidth: 1)
        )
    }
}

// MARK: - Preview Provider
struct SpeedOptionsPopover_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SpeedOptionsPopover(
                currentSpeed: 1.0,
                theme: .light,
                onSpeedSelected: { _ in }
            )
            .previewDisplayName("Light Theme")
            
            SpeedOptionsPopover(
                currentSpeed: 1.5,
                theme: .dark,
                onSpeedSelected: { _ in }
            )
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 
