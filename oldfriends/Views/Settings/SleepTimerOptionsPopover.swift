//
//  SleepTimerOptionsPopover.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - SleepTimerOptionsPopover
struct SleepTimerOptionsPopover: View {
    // MARK: - Properties
    @ObservedObject var viewModel: PlayerViewModel
    let theme: AppTheme
    let onOptionSelected: (TimeInterval?) -> Void
    
    private let timerOptions: [(label: String, icon: String, duration: TimeInterval?)] = [
        ("关闭", "xmark.circle", nil),
        ("15分钟", "timer", 15 * 60),
        ("30分钟", "timer.circle", 30 * 60),
        ("45分钟", "timer.square", 45 * 60),
        ("60分钟", "clock", 60 * 60),
        ("90分钟", "clock.fill", 90 * 60)
    ]
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            ForEach(timerOptions, id: \.label) { option in
                Button(action: {
                    if let duration = option.duration {
                        viewModel.setSleepTimer(duration)
                    } else {
                        viewModel.cancelSleepTimer()
                    }
                    onOptionSelected(option.duration)
                }) {
                    HStack {
                        Image(systemName: option.icon)
                            .font(.system(size: 15))
                            .foregroundColor(ThemeColors.primary)
                            .frame(width: 22)
                        
                        Text(option.label)
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.primaryText(theme))
                        
                        Spacer()
                        
                        if viewModel.sleepTimer != nil &&
                            ((option.duration == nil && viewModel.remainingSleepTime == 0) ||
                             (option.duration != nil && viewModel.remainingSleepTime > 0 &&
                              viewModel.remainingSleepTime <= option.duration!)) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12))
                                .foregroundColor(ThemeColors.primary)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                )
                
                if option.label != timerOptions.last?.label {
                    Divider()
                        .padding(.horizontal, 12)
                }
            }
        }
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(ThemeColors.background(theme))
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 3)
        )
        .frame(width: 140)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(ThemeColors.border(theme), lineWidth: 1)
        )
    }
}

// MARK: - Preview Provider
struct SleepTimerOptionsPopover_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SleepTimerOptionsPopover(
                viewModel: PlayerViewModel(),
                theme: .light,
                onOptionSelected: { _ in }
            )
            .previewDisplayName("Light Theme")
            
            SleepTimerOptionsPopover(
                viewModel: PlayerViewModel(),
                theme: .dark,
                onOptionSelected: { _ in }
            )
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 
