//
//  SubtitleLanguageOptionsPopover.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - SubtitleLanguageOptionsPopover
struct SubtitleLanguageOptionsPopover: View {
    // MARK: - Properties
    @ObservedObject var viewModel: PlayerViewModel
    let theme: AppTheme
    let onOptionSelected: (SubtitleDisplayMode) -> Void
    
    private let languageOptions: [(label: String, icon: String, mode: SubtitleDisplayMode)] = [
        ("仅中文", "a.square", .chineseOnly),
        ("仅英文", "textformat.abc", .englishOnly),
        ("中文英文", "character.book.closed", .both),
        ("不显示", "text.badge.xmark", .none)
    ]
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            ForEach(languageOptions, id: \.label) { option in
                Button(action: {
                    // print("[SubtitleLanguageOptionsPopover] 用户选择了: \(option.label)")
                    // print("[SubtitleLanguageOptionsPopover] 当前模式: \(viewModel.subtitleDisplayMode)")
                    
                    viewModel.setSubtitleDisplayMode(option.mode)
                    onOptionSelected(option.mode)
                    
                    // print("[SubtitleLanguageOptionsPopover] 选择后模式: \(viewModel.subtitleDisplayMode)")
                }) {
                    HStack {
                        Image(systemName: option.icon)
                            .font(.system(size: 15))
                            .foregroundColor(ThemeColors.primary)
                            .frame(width: 22)
                        
                        Text(option.label)
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.primaryText(theme))
                        
                        Spacer()
                        
                        if viewModel.subtitleDisplayMode == option.mode {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12))
                                .foregroundColor(ThemeColors.primary)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                )
                
                if option.label != languageOptions.last?.label {
                    Divider()
                        .padding(.horizontal, 12)
                }
            }
        }
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(ThemeColors.background(theme))
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 3)
        )
        .frame(width: 140)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(ThemeColors.border(theme), lineWidth: 1)
        )
    }
}

// MARK: - Preview Provider
struct SubtitleLanguageOptionsPopover_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SubtitleLanguageOptionsPopover(
                viewModel: PlayerViewModel(),
                theme: .light,
                onOptionSelected: { _ in }
            )
            .previewDisplayName("Light Theme")
            
            SubtitleLanguageOptionsPopover(
                viewModel: PlayerViewModel(),
                theme: .dark,
                onOptionSelected: { _ in }
            )
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 