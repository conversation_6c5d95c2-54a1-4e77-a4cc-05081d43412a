import SwiftUI

// MARK: - ThemeOption
enum ThemeOption: String, CaseIterable {
    case system = "跟随系统"
    case light = "浅色"
    case dark = "深色"
    
    var icon: String {
        switch self {
        case .system: return "circle.lefthalf.filled"
        case .light: return "sun.max.fill"
        case .dark: return "moon.fill"
        }
    }
}

// MARK: - ThemeSettingsView
struct ThemeSettingsView: View {
    @Binding var isShowing: Bool
    @ObservedObject var viewModel: PlayerViewModel
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach(ThemeOption.allCases, id: \.self) { option in
                Button(action: {
                    handleThemeSelection(option)
                }) {
                    HStack {
                        Image(systemName: option.icon)
                            .font(.system(size: 15))
                            .foregroundColor(ThemeColors.primary)
                            .frame(width: 22)
                        
                        Text(option.rawValue)
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                        
                        Spacer()
                        
                        if isCurrentTheme(option) {
                            Image(systemName: "checkmark")
                                .font(.system(size: 12))
                                .foregroundColor(ThemeColors.primary)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                )
                
                if option != ThemeOption.allCases.last {
                    Divider()
                        .padding(.horizontal, 12)
                }
            }
        }
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(ThemeColors.background(viewModel.currentTheme))
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 3)
        )
        .frame(width: 140)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(ThemeColors.border(viewModel.currentTheme), lineWidth: 1)
        )
    }
    
    // MARK: - Private Methods
    private func handleThemeSelection(_ option: ThemeOption) {
        switch option {
        case .system:
            viewModel.isThemeManuallySet = false
            viewModel.currentTheme = colorScheme == .dark ? .dark : .light
            UserDefaults.standard.removeObject(forKey: "app_theme")
            UserDefaults.standard.set(false, forKey: "theme_manually_set")
        case .light:
            viewModel.isThemeManuallySet = true
            viewModel.currentTheme = .light
            UserDefaults.standard.set("light", forKey: "app_theme")
            UserDefaults.standard.set(true, forKey: "theme_manually_set")
        case .dark:
            viewModel.isThemeManuallySet = true
            viewModel.currentTheme = .dark
            UserDefaults.standard.set("dark", forKey: "app_theme")
            UserDefaults.standard.set(true, forKey: "theme_manually_set")
        }
        isShowing = false
    }
    
    private func isCurrentTheme(_ option: ThemeOption) -> Bool {
        switch option {
        case .system:
            return !viewModel.isThemeManuallySet
        case .light:
            return viewModel.isThemeManuallySet && viewModel.currentTheme == .light
        case .dark:
            return viewModel.isThemeManuallySet && viewModel.currentTheme == .dark
        }
    }
}

// MARK: - Preview Provider
struct ThemeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ThemeSettingsView(
                isShowing: .constant(true),
                viewModel: PlayerViewModel()
            )
            .previewDisplayName("Light Theme")
            
            ThemeSettingsView(
                isShowing: .constant(true),
                viewModel: PlayerViewModel()
            )
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 
