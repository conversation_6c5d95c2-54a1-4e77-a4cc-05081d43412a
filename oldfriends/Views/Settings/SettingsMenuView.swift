//
//  SettingsMenuView.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI
import UIKit

// MARK: - SettingMenuItem
enum SettingMenuItem: CaseIterable {
    case theme
    case playbackSpeed
    case subtitleLanguage
    case sleepTimer

    var title: String {
        switch self {
        case .theme: return "主题"
        case .playbackSpeed: return "倍速"
        case .subtitleLanguage: return "字幕"
        case .sleepTimer: return "定时"
        }
    }

    var icon: String {
        switch self {
        case .theme: return "paintbrush.fill"
        case .playbackSpeed: return "speedometer"
        case .subtitleLanguage: return "captions.bubble.fill"
        case .sleepTimer: return "timer.circle.fill"
        }
    }

    func iconColor(theme: AppTheme) -> Color {
        return ThemeColors.primary
    }
}

// MARK: - SettingsMenuView
struct SettingsMenuView: View {
    // MARK: - Properties
    @ObservedObject var viewModel: PlayerViewModel
    @Binding var isShowing: Bool

    // MARK: - Body
    var body: some View {
        HStack(spacing: 0) {
            ForEach(SettingMenuItem.allCases, id: \.self) { item in
                Menu {
                    menuContent(for: item)
                } label: {
                    VStack(spacing: 2) {
                        ZStack {
                            Circle()
                                .fill(item.iconColor(theme: viewModel.currentTheme).opacity(0.15))
                                .frame(width: 32, height: 32) // 增大图标背景

                            Image(systemName: item.icon)
                                .font(.system(size: 15, weight: .semibold)) // 增大图标和字重
                                .foregroundColor(item.iconColor(theme: viewModel.currentTheme))
                        }

                        Text(item.title)
                            .font(.system(size: 12, weight: .semibold)) // 增强主菜单文字权重
                            .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                            .multilineTextAlignment(.center)
                            .lineLimit(1)

                        // 状态信息显示
                        if item == .sleepTimer && viewModel.remainingSleepTime > 0 {
                            Text(formatRemainingTime(viewModel.remainingSleepTime))
                                .font(.system(size: 9))
                                .foregroundColor(ThemeColors.secondaryText(viewModel.currentTheme))
                        } else {
                            // 占位文本，保持高度一致
                            Text(" ")
                                .font(.system(size: 9))
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: 64)
                    .padding(.vertical, 4)
                    .padding(.horizontal, 2)
                    .contentShape(Rectangle())
                }

                if item != SettingMenuItem.allCases.last {
                    Rectangle()
                        .fill(ThemeColors.border(viewModel.currentTheme))
                        .frame(width: 1)
                        .opacity(0.6)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .frame(height: 72) // 固定高度，更紧凑
        .background(
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(ThemeColors.surface(viewModel.currentTheme))

                RoundedRectangle(cornerRadius: 12)
                    .stroke(ThemeColors.border(viewModel.currentTheme), lineWidth: 1)
            }
            .shadow(color: Color.black.opacity(0.12), radius: 8, x: 0, y: 3)
        )
        .frame(maxWidth: .infinity)
        .frame(height: 80)
    }

    // MARK: - Private Methods
    @ViewBuilder
    private func menuContent(for item: SettingMenuItem) -> some View {
        switch item {
        case .theme:
            themeMenuContent()
        case .playbackSpeed:
            speedMenuContent()
        case .subtitleLanguage:
            subtitleLanguageMenuContent()
        case .sleepTimer:
            sleepTimerMenuContent()
        }
    }

    @ViewBuilder
    private func themeMenuContent() -> some View {
        Button(action: {
            viewModel.isThemeManuallySet = false
            viewModel.currentTheme = UITraitCollection.current.userInterfaceStyle == .dark ? .dark : .light
            UserDefaults.standard.removeObject(forKey: "app_theme")
            UserDefaults.standard.set(false, forKey: "theme_manually_set")
        }) {
            Label("跟随系统", systemImage: "circle.lefthalf.filled")
        }

        Button(action: {
            viewModel.isThemeManuallySet = true
            viewModel.currentTheme = .light
            UserDefaults.standard.set("light", forKey: "app_theme")
            UserDefaults.standard.set(true, forKey: "theme_manually_set")
        }) {
            Label("浅色", systemImage: "sun.max.fill")
        }

        Button(action: {
            viewModel.isThemeManuallySet = true
            viewModel.currentTheme = .dark
            UserDefaults.standard.set("dark", forKey: "app_theme")
            UserDefaults.standard.set(true, forKey: "theme_manually_set")
        }) {
            Label("深色", systemImage: "moon.fill")
        }
    }

    @ViewBuilder
    private func speedMenuContent() -> some View {
        let speedOptions: [(label: String, value: Double, icon: String)] = [
            ("0.5x 减速", 0.5, "tortoise.fill"),
            ("0.75x 慢速", 0.75, "hare.fill"),
            ("1.0x 正常", 1.0, "figure.walk"),
            ("1.25x 快速", 1.25, "figure.run"),
            ("1.5x 加速", 1.5, "bolt.fill"),
            ("2.0x 最快", 2.0, "bolt.horizontal.fill")
        ]

        ForEach(speedOptions, id: \.value) { option in
            Button(action: {
                viewModel.setPlaybackSpeed(option.value)
            }) {
                Label(option.label, systemImage: option.icon)
            }
        }
    }

    @ViewBuilder
    private func subtitleLanguageMenuContent() -> some View {
        let languageOptions: [(label: String, icon: String, mode: SubtitleDisplayMode)] = [
            ("仅中文", "a.square", .chineseOnly),
            ("仅英文", "textformat.abc", .englishOnly),
            ("中文英文", "character.book.closed", .both),
            ("不显示", "text.badge.xmark", .none)
        ]

        ForEach(languageOptions, id: \.mode) { option in
            Button(action: {
                viewModel.setSubtitleDisplayMode(option.mode)
            }) {
                Label(option.label, systemImage: option.icon)
            }
        }
    }

    @ViewBuilder
    private func sleepTimerMenuContent() -> some View {
        let timerOptions: [(label: String, icon: String, duration: TimeInterval?)] = [
            ("关闭", "xmark.circle", nil),
            ("15分钟", "timer", 15 * 60),
            ("30分钟", "timer.circle", 30 * 60),
            ("45分钟", "timer.square", 45 * 60),
            ("60分钟", "clock", 60 * 60),
            ("90分钟", "clock.fill", 90 * 60)
        ]

        ForEach(timerOptions, id: \.label) { option in
            Button(action: {
                if let duration = option.duration {
                    viewModel.setSleepTimer(duration)
                } else {
                    viewModel.cancelSleepTimer()
                }
            }) {
                Label(option.label, systemImage: option.icon)
            }
        }
    }

    private func formatRemainingTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private func getCurrentThemeText() -> String {
        if !viewModel.isThemeManuallySet {
            return "系统"
        } else {
            return viewModel.currentTheme == .dark ? "深色" : "浅色"
        }
    }
}

// MARK: - Preview Provider
struct SettingsMenuView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SettingsMenuView(
                viewModel: PlayerViewModel(),
                isShowing: .constant(true)
            )
            .previewDisplayName("Light Theme")

            SettingsMenuView(
                viewModel: PlayerViewModel(),
                isShowing: .constant(true)
            )
            .preferredColorScheme(.dark)
            .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
}