//
//  FlowLayout.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

/// 流式布局组件，用于自动换行显示内容
/// 可以根据容器宽度自动调整内容布局
struct FlowLayout<Content: View>: View {
    // MARK: - Properties
    let items: [String]
    let spacing: CGFloat
    let onTapWord: (String) -> Void
    let wordBuilder: (String) -> Content
    
    // MARK: - Initialization
    init(
        items: [String],
        spacing: CGFloat,
        onTapWord: @escaping (String) -> Void,
        @ViewBuilder wordBuilder: @escaping (String) -> Content
    ) {
        self.items = items
        self.spacing = spacing
        self.onTapWord = onTapWord
        self.wordBuilder = wordBuilder
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            generateContent(in: geometry)
        }
    }
    
    // MARK: - Private Methods
    private func generateContent(in geometry: GeometryProxy) -> some View {
        var width = CGFloat.zero
        var height = CGFloat.zero
        var rowCount = 0
        let maxWidth = geometry.size.width
        
        return ZStack(alignment: .topLeading) {
            ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                wordBuilder(item)
                    .padding(.horizontal, spacing)
                    .alignmentGuide(.leading) { dimension in
                        if abs(width - dimension.width) > maxWidth {
                            width = 0
                            height -= dimension.height
                            rowCount += 1
                        }
                        let result = width
                        if index == items.count - 1 {
                            width = 0
                        } else {
                            width -= dimension.width
                        }
                        return result
                    }
                    .alignmentGuide(.top) { _ in
                        let result = height
                        if index == items.count - 1 {
                            height = 0
                        }
                        return result
                    }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    FlowLayout(
        items: ["Hello", "World", "This", "is", "a", "Flow", "Layout", "Example"],
        spacing: 8,
        onTapWord: { _ in }
    ) { word in
        Text(word)
            .padding(8)
            .background(Color.blue.opacity(0.2))
            .cornerRadius(4)
    }
    .padding()
    .frame(height: 200)
} 