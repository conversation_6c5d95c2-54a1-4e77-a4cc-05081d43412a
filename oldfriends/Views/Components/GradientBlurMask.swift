//
//  GradientBlurMask.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - GradientBlurMask
struct GradientBlurMask: View {
    // MARK: - Properties
    let theme: AppTheme
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 主渐变
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: maskColor.opacity(0.75), location: 0),
                    .init(color: maskColor.opacity(0.5), location: 0.3),
                    .init(color: maskColor.opacity(0), location: 0.6)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            
            // 额外的模糊效果层
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: maskColor.opacity(0.2), location: 0),
                    .init(color: maskColor.opacity(0), location: 0.4)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .blur(radius: 6)
        }
        .allowsHitTesting(false)
    }
    
    // MARK: - Private Helpers
    private var maskColor: Color {
        theme == .dark ? .black : .white
    }
}

// MARK: - Preview Provider
struct GradientBlurMask_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Light theme preview
            GradientBlurMask(theme: .light)
                .frame(height: 120)
                .previewDisplayName("Light Theme")
            
            // Dark theme preview
            GradientBlurMask(theme: .dark)
                .frame(height: 120)
                .preferredColorScheme(.dark)
                .previewDisplayName("Dark Theme")
        }
    }
} 