//
//  PlayerRightControls.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - PlayerRightControls
struct PlayerRightControls: View {
    // MARK: - Properties
    let theme: AppTheme
    let playbackSpeed: Double
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: 24) {
            But<PERSON>(action: {}) {
                Image(systemName: "house")
                    .foregroundColor(.white)
            }
            
            <PERSON><PERSON>(action: {}) {
                Image(systemName: "list.bullet")
                    .foregroundColor(.white)
            }
            
            But<PERSON>(action: {}) {
                Text("x\(String(format: "%.1f", playbackSpeed))")
                    .foregroundColor(.white)
                    .font(.system(size: 14))
            }
            
            But<PERSON>(action: {}) {
                Image(systemName: "doc.text")
                    .foregroundColor(.white)
            }
        }
    }
}

// MARK: - Preview Provider
struct PlayerRightControls_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            PlayerRightControls(theme: .light, playbackSpeed: 1.0)
                .previewDisplayName("Light Theme")
            
            PlayerRightControls(theme: .dark, playbackSpeed: 1.5)
                .preferredColorScheme(.dark)
                .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 