//
//  PlayerLeftControls.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

// MARK: - PlayerLeftControls
struct PlayerLeftControls: View {
    // MARK: - Properties
    let theme: AppTheme
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: 24) {
            <PERSON><PERSON>(action: {}) {
                Image(systemName: "star")
                    .foregroundColor(.white)
            }
            
            <PERSON><PERSON>(action: {}) {
                Image(systemName: "info.circle")
                    .foregroundColor(.white)
            }
            
            <PERSON><PERSON>(action: {}) {
                Text("N")
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .medium))
            }
        }
    }
}

// MARK: - Preview Provider
struct PlayerLeftControls_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            PlayerLeftControls(theme: .light)
                .previewDisplayName("Light Theme")
            
            PlayerLeftControls(theme: .dark)
                .preferredColorScheme(.dark)
                .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
} 