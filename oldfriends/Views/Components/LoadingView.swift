//
//  LoadingView.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

struct LoadingView: View {
    let message: String
    
    // MARK: - Body
    var body: some View {
        ZStack {
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
            
            ProgressView(message)
                .padding()
                .background(Color(UIColor.systemBackground))
                .cornerRadius(8)
                .shadow(radius: 4)
        }
    }
}

// MARK: - Preview
#Preview {
    LoadingView(message: "正在加载...")
} 