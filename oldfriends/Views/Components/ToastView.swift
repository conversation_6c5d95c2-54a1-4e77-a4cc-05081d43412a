//
//  ToastView.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI

struct ToastView: View {
    let message: String
    
    // MARK: - Body
    var body: some View {
        VStack {
            Spacer()
            Text(message)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.black.opacity(0.8))
                .cornerRadius(8)
                .padding(.bottom, 100)
        }
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }
}

// MARK: - Preview
#Preview {
    ToastView(message: "这是一条测试消息")
} 