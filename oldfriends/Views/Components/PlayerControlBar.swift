//
//  PlayerControlBar.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI
import AVFoundation

// MARK: - PlayerControlBar
struct PlayerControlBar: View {
    @ObservedObject var viewModel: PlayerViewModel
    @EnvironmentObject var wordSelectionManager: WordSelectionManager

    // MARK: - Properties
    @State private var showSettings = false
    @State private var settingsPosition: CGPoint = .zero
    @State private var autoHideTimer: Timer?
    private let autoHideDelay: TimeInterval = 10.0

    @State private var showSpeedOptionsPopover = false
    @State private var speedOptionsTimer: Timer?

    @State private var showSubtitleLanguageOptions = false
    @State private var subtitleOptionsTimer: Timer?

    // MARK: - Body
    var body: some View {
        VStack(spacing: 2) {
            // 设置菜单显示在最上方
            if showSettings {
                settingsMenuView
                    .fixedSize(horizontal: false, vertical: true) // 固定垂直尺寸
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .animation(.spring(response: 0.3), value: showSettings)
            }

            // 状态指示器
            if hasActiveIndicators {
                statusIndicatorsView
                    .padding(.top, 1)
            }
            progressBarView
            controlButtonsView
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 16)
        .onDisappear {
            cleanupTimers()
        }
    }

    // 判断是否有活跃的指示器需要显示
    private var hasActiveIndicators: Bool {
        return viewModel.playMode == .abLoop ||
               viewModel.playMode == .singleSentence ||
               viewModel.playbackSpeed != 1.0 ||
               viewModel.subtitleDisplayMode != .both
    }

    // MARK: - Private Views
    private var progressBarView: some View {
        ProgressBar(
            value: viewModel.currentTime,
            total: viewModel.totalTime,
            onSeek: { newTime in
                viewModel.seek(to: newTime)
            }
        )
        .frame(height: viewModel.playMode == .sequential ? 14 : 32)
        .padding(.horizontal, 8)
    }

    private var controlButtonsView: some View {
        HStack {
            settingsButton
            Spacer()
            playbackControlButtons
            Spacer()
            loopModeButton
        }
        .padding(.vertical, 1)
    }

    // MARK: - Control Buttons
    private var settingsButton: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3)) {
                showSettings.toggle()
            }
            if showSettings {
                startAutoHideTimer()
            } else {
                autoHideTimer?.invalidate()
                autoHideTimer = nil
            }
        }) {
            ZStack {
                Circle()
                    .fill(ThemeColors.primary.opacity(showSettings ? 0.25 : 0.15))
                    .frame(width: 36, height: 36) // 统一按钮大小

                Image(systemName: "slider.horizontal.3")
                    .font(.system(size: 16, weight: .medium)) // 统一图标大小
                    .foregroundColor(ThemeColors.primary)
            }
        }
        .padding(.leading, 8)
    }

    private var playbackControlButtons: some View {
        HStack(spacing: 28) {
            previousButton
            playPauseButton
            nextButton
        }
    }

    private var loopModeButton: some View {
        Menu {
            Button(action: {
                withAnimation {
                    viewModel.playMode = .sequential
                }
            }) {
                HStack {
                    Image(systemName: PlayMode.sequential.icon)
                    Text(PlayMode.sequential.description)
                    Spacer()
                    if viewModel.playMode == .sequential {
                        Image(systemName: "checkmark")
                            .foregroundColor(ThemeColors.primary)
                    }
                }
            }

            Button(action: {
                withAnimation {
                    viewModel.playMode = .singleSentence
                }
            }) {
                HStack {
                    Image(systemName: PlayMode.singleSentence.icon)
                    Text(PlayMode.singleSentence.description)
                    Spacer()
                    if viewModel.playMode == .singleSentence {
                        Image(systemName: "checkmark")
                            .foregroundColor(ThemeColors.primary)
                    }
                }
            }

            Button(action: {
                withAnimation {
                    viewModel.playMode = .singleEpisode
                }
            }) {
                HStack {
                    Image(systemName: PlayMode.singleEpisode.icon)
                    Text(PlayMode.singleEpisode.description)
                    Spacer()
                    if viewModel.playMode == .singleEpisode {
                        Image(systemName: "checkmark")
                            .foregroundColor(ThemeColors.primary)
                    }
                }
            }

            Button(action: {
                withAnimation {
                    viewModel.playMode = .abLoop
                }
            }) {
                HStack {
                    Image(systemName: PlayMode.abLoop.icon)
                    Text(PlayMode.abLoop.description)
                    Spacer()
                    if viewModel.playMode == .abLoop {
                        Image(systemName: "checkmark")
                            .foregroundColor(ThemeColors.primary)
                    }
                }
            }
        } label: {
            ZStack {
                Circle()
                    .fill(ThemeColors.primary.opacity(0.1))
                    .frame(width: 36, height: 36) // 统一按钮大小和背景

                Image(systemName: viewModel.playMode.icon)
                    .font(.system(size: 16, weight: .medium)) // 统一字重
                    .foregroundColor(ThemeColors.primary)
            }
        }
        .padding(.trailing, 8)
    }

    // MARK: - Helper Views
    private var settingsMenuView: some View {
        SettingsMenuView(viewModel: viewModel, isShowing: $showSettings)
            .onAppear {
                startAutoHideTimer()
            }
            .onDisappear {
                autoHideTimer?.invalidate()
                autoHideTimer = nil
            }
    }

    // MARK: - Private Methods
    private func startAutoHideTimer() {
        autoHideTimer?.invalidate()
        autoHideTimer = Timer.scheduledTimer(withTimeInterval: autoHideDelay, repeats: false) { _ in
            withAnimation(.spring(response: 0.3)) {
                showSettings = false
            }
        }
    }

    private func startSpeedOptionsTimer() {
        speedOptionsTimer?.invalidate()
        speedOptionsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.spring(response: 0.3)) {
                showSpeedOptionsPopover = false
            }
            speedOptionsTimer = nil
        }
    }

    private func startSubtitleOptionsTimer() {
        subtitleOptionsTimer?.invalidate()
        subtitleOptionsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.spring(response: 0.3)) {
                showSubtitleLanguageOptions = false
            }
            subtitleOptionsTimer = nil
        }
    }

    private func cleanupTimers() {
        speedOptionsTimer?.invalidate()
        speedOptionsTimer = nil

        subtitleOptionsTimer?.invalidate()
        subtitleOptionsTimer = nil

        autoHideTimer?.invalidate()
        autoHideTimer = nil
    }

    // MARK: - Playback Control Buttons
    private var previousButton: some View {
        Button(action: {
            // 关闭查词气泡
            wordSelectionManager.selectedWord = nil
            // 播放上一句
            if let currentSubtitle = viewModel.getCurrentSubtitle() ?? viewModel.getNearestSubtitle(),
               let currentIndex = viewModel.subtitles.firstIndex(where: { $0.id == currentSubtitle.id }),
               currentIndex > 0 {
                let previousSubtitle = viewModel.subtitles[currentIndex - 1]
                // 根据当前播放模式处理
                if viewModel.playMode == .singleSentence {
                    // print("[PlayControl] 单句循环模式 - 切换到上一句: \(previousSubtitle.english)")
                    viewModel.playSubtitle(previousSubtitle)
                } else {
                    viewModel.playSubtitle(previousSubtitle)
                }
            }
        }) {
            ZStack {
                Circle()
                    .fill(ThemeColors.primaryText(viewModel.currentTheme).opacity(0.1))
                    .frame(width: 36, height: 36) // 统一按钮大小和背景

                Image(systemName: "backward.fill")
                    .font(.system(size: 16, weight: .medium)) // 统一图标大小和字重
                    .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
            }
        }
    }

    private var playPauseButton: some View {
        Button(action: {
            wordSelectionManager.selectedWord = nil
            viewModel.togglePlayPause()
        }) {
            Image(systemName: viewModel.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                .font(.system(size: 36))
                .foregroundColor(ThemeColors.primary)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
    }

    private var nextButton: some View {
        Button(action: {
            // 关闭查词气泡
            wordSelectionManager.selectedWord = nil
            // 播放下一句
            if let currentSubtitle = viewModel.getCurrentSubtitle() ?? viewModel.getNearestSubtitle(),
               let currentIndex = viewModel.subtitles.firstIndex(where: { $0.id == currentSubtitle.id }),
               currentIndex < viewModel.subtitles.count - 1 {
                let nextSubtitle = viewModel.subtitles[currentIndex + 1]
                // 根据当前播放模式处理
                if viewModel.playMode == .singleSentence {
                    // print("[PlayControl] 单句循环模式 - 切换到下一句: \(nextSubtitle.english)")
                    viewModel.playSubtitle(nextSubtitle)
                } else {
                    viewModel.playSubtitle(nextSubtitle)
                }
            }
        }) {
            ZStack {
                Circle()
                    .fill(ThemeColors.primaryText(viewModel.currentTheme).opacity(0.1))
                    .frame(width: 36, height: 36) // 统一按钮大小和背景

                Image(systemName: "forward.fill")
                    .font(.system(size: 16, weight: .medium)) // 统一图标大小和字重
                    .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
            }
        }
    }

    // MARK: - Status Indicators View
    private var statusIndicatorsView: some View {
        HStack(spacing: 8) {
            Spacer()
            // AB 循环指示器
            if viewModel.playMode == .abLoop {
                Menu {
                    Button(action: {
                        viewModel.pausePlaybackAndResetABLoop()
                    }) {
                        Label("重新设置", systemImage: "arrow.clockwise")
                    }
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.left.and.right.circle")
                            .font(.system(size: 12, weight: .medium)) // 统一图标样式
                        Text(viewModel.abLoopState == .looping ? "AB循环" : "设置AB")
                            .font(.system(size: 12, weight: .medium)) // 统一文字样式，简化文案
                            .foregroundColor(.orange)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.orange.opacity(0.15))
                            .overlay(
                                Capsule()
                                    .stroke(Color.orange.opacity(0.3), lineWidth: 0.5)
                            )
                    )
                }
            }

            // 单句循环指示器
            if viewModel.playMode == .singleSentence {
                HStack(spacing: 4) {
                    Image(systemName: "repeat.1.circle")
                        .font(.system(size: 12, weight: .medium)) // 统一图标样式
                    Text("单句")
                        .font(.system(size: 12, weight: .medium)) // 统一文字样式，简化文案
                        .foregroundColor(ThemeColors.primary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(ThemeColors.primary.opacity(0.15))
                        .overlay(
                            Capsule()
                                .stroke(ThemeColors.primary.opacity(0.3), lineWidth: 0.5)
                        )
                )
            }

            // 速度指示器
            if viewModel.playbackSpeed != 1.0 {
                Button(action: {
                    showSpeedOptionsPopover = true
                    startSpeedOptionsTimer()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "speedometer")
                            .font(.system(size: 12, weight: .medium)) // 统一图标样式
                        Text("\(String(format: "%.1f", viewModel.playbackSpeed))×")
                            .font(.system(size: 12, weight: .medium)) // 统一文字样式，使用正确的乘号
                            .foregroundColor(ThemeColors.primary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(ThemeColors.primary.opacity(0.15))
                            .overlay(
                                Capsule()
                                    .stroke(ThemeColors.primary.opacity(0.3), lineWidth: 0.5)
                            )
                    )
                }
                .overlay(
                    Group {
                        if showSpeedOptionsPopover {
                            SpeedOptionsPopover(
                                currentSpeed: viewModel.playbackSpeed,
                                theme: viewModel.currentTheme
                            ) { speed in
                                viewModel.setPlaybackSpeed(speed)
                                showSpeedOptionsPopover = false
                                speedOptionsTimer?.invalidate()
                                speedOptionsTimer = nil
                            }
                            .offset(y: -150)
                            .transition(.scale.combined(with: .opacity))
                        }
                    }
                )
            }

            // 字幕语言指示器
            if viewModel.subtitleDisplayMode != .both {
                Button(action: {
                    showSubtitleLanguageOptions = true
                    startSubtitleOptionsTimer()
                }) {
                    HStack(spacing: 3) {
                        Image(systemName: "text.bubble")
                            .font(.system(size: 14))
                        Text(getSubtitleModeText(viewModel.subtitleDisplayMode))
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.primary)
                    }
                    .padding(.horizontal, 5)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(ThemeColors.primary.opacity(0.15)))
                }
                .overlay(
                    Group {
                        if showSubtitleLanguageOptions {
                            SubtitleLanguageOptionsPopover(
                                viewModel: viewModel,
                                theme: viewModel.currentTheme
                            ) { mode in
                                viewModel.setSubtitleDisplayMode(mode)
                                showSubtitleLanguageOptions = false
                                subtitleOptionsTimer?.invalidate()
                                subtitleOptionsTimer = nil
                            }
                            .offset(y: -110)
                            .transition(.scale.combined(with: .opacity))
                        }
                    }
                )
            }
            Spacer()
        }
        .padding(.horizontal, 8)
        .animation(.spring(response: 0.3), value: viewModel.playMode)
        .animation(.spring(response: 0.3), value: viewModel.playbackSpeed)
        .animation(.spring(response: 0.3), value: showSpeedOptionsPopover)
        .animation(.spring(response: 0.3), value: viewModel.subtitleDisplayMode)
        .animation(.spring(response: 0.3), value: showSubtitleLanguageOptions)
    }

    // 添加一个辅助方法来获取字幕模式的显示文本
    private func getSubtitleModeText(_ mode: SubtitleDisplayMode) -> String {
        switch mode {
        case .chineseOnly:
            return "仅中文"
        case .englishOnly:
            return "仅英文"
        case .both:
            return "中英文"
        case .none:
            return "隐藏"
        }
    }
}

// MARK: - Preview Provider
struct PlayerControlBar_Previews: PreviewProvider {
    static var previews: some View {
        PlayerControlBar(viewModel: PlayerViewModel())
            .environmentObject(WordSelectionManager())
    }
}