//
//  PlayerTopBarView.swift
//  DramaLingo
//
//  Created by weile on 2024/10/28.
//

import SwiftUI
import StoreHelper

// MARK: - PlayerTopBarView
struct PlayerTopBarView: View {
    // MARK: - Properties
    let theme: AppTheme
    @EnvironmentObject private var favoriteManager: FavoriteManager
    @EnvironmentObject private var viewModel: PlayerViewModel
    @State private var showEpisodeSheet = false
    @State private var showExportMenu = false
    @State private var isExporting = false
    @State private var exportedURL: URL?
    @State private var showShareSheet = false
    @State private var documentURL: URL?
    @State private var showDocumentPicker = false
    @State private var isEpisodeFavorited = false

    // MARK: - Body
    var body: some View {
        HStack {
            Button(action: {
                showEpisodeSheet = true
            }) {
                ZStack {
                    // 背景圆角矩形
                    RoundedRectangle(cornerRadius: 12)
                        .fill(ThemeColors.primary.opacity(0.1))
                        .frame(width: 40, height: 40)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(ThemeColors.primary.opacity(0.2), lineWidth: 1)
                        )

                    // 图标
                    Image(systemName: "square.grid.2x2")
                        .foregroundColor(ThemeColors.primary)
                        .font(.system(size: 18, weight: .medium))
                }
                .shadow(color: ThemeColors.primary.opacity(0.15), radius: 4, x: 0, y: 2)
            }

            Spacer()

            HStack(spacing: 16) {
                // 季集标签 - 现代化设计
                HStack(spacing: 4) {
                    // 季标签
                    Text("S\(viewModel.currentSeasonNumber)")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(ThemeColors.primary)
                        )

                    // 集标签
                    Text("E\(viewModel.currentEpisodeNumber)")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(ThemeColors.primary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(ThemeColors.primary.opacity(0.15))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(ThemeColors.primary.opacity(0.3), lineWidth: 1)
                                )
                        )
                }

                // 标题 - 增强视觉权重，保持简洁
                Text(viewModel.currentEpisodeTitle)
                    .foregroundColor(ThemeColors.primaryText(theme))
                    .font(.system(size: 18, weight: .semibold)) // 增大字号和字重
                    .lineLimit(2) // 允许两行显示
                    .multilineTextAlignment(.leading)
            }

            Spacer()

            // 右侧功能按钮组 - 统一设计风格
            HStack(spacing: 12) {
                NavigationLink {
                    FavoriteWordsView()
                        .environmentObject(favoriteManager)
                        .environmentObject(viewModel)
                        .onAppear {
                            if viewModel.isPlaying {
                                viewModel.togglePlayPause()
                            }
                        }
                } label: {
                    ZStack {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(ThemeColors.primary.opacity(0.1))
                            .frame(width: 36, height: 36)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(ThemeColors.primary.opacity(0.2), lineWidth: 1)
                            )

                        Image(systemName: "star.fill")
                            .foregroundColor(ThemeColors.primary)
                            .font(.system(size: 16, weight: .semibold))
                    }
                }
                .simultaneousGesture(TapGesture().onEnded {
                    if viewModel.isPlaying {
                        viewModel.togglePlayPause()
                    }
                })

                Button(action: {
                    Task {
                        // 添加导出 PDF 点击埋点
                        AnalyticsManager.shared.logExportPDFClick()
                        await exportSubtitles()
                    }
                }) {
                    ZStack {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(ThemeColors.primary.opacity(0.1))
                            .frame(width: 36, height: 36)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(ThemeColors.primary.opacity(0.2), lineWidth: 1)
                            )

                        Image(systemName: "printer.fill")
                            .foregroundColor(ThemeColors.primary)
                            .font(.system(size: 16, weight: .semibold))
                    }
                }


            }
        }
        .padding(.vertical, 16) // 增加垂直内边距
        .padding(.horizontal, 20) // 增加水平内边距
        .background(
            // 使用渐变背景增强视觉层次
            LinearGradient(
                gradient: Gradient(colors: [
                    ThemeColors.surface(theme),
                    ThemeColors.surface(theme).opacity(0.98)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(ThemeColors.border(theme))
                .opacity(0.4),
            alignment: .bottom
        )
        .shadow(
            color: theme == .dark ? Color.black.opacity(0.4) : Color.black.opacity(0.08),
            radius: 8,
            x: 0,
            y: 4
        )
        .sheet(isPresented: $showEpisodeSheet) {
            ShowDrawerView(theme: theme)
                .presentationDetents([.large])
        }
        .sheet(isPresented: $showShareSheet) {
            if let url = exportedURL {
                ShareSheet(activityItems: [url])
            }
        }
    }

    // MARK: - Private Methods
    private func exportSubtitles() async {
        // print("[PlayerTopBar:Export] 开始导出字幕")

        await MainActor.run {
            viewModel.isExporting = true
        }

        defer {
            Task { @MainActor in
                viewModel.isExporting = false
            }
        }

        if let url = await PDFGenerator.generateSubtitlesPDF(
            subtitles: viewModel.subtitles,
            seasonNumber: viewModel.currentSeasonNumber,
            episodeNumber: viewModel.currentEpisodeNumber,
            theme: viewModel.currentTheme
        ) {
            // print("[PlayerTopBar:Export] PDF生成成功，文件路径: \(url)")
            await MainActor.run {
                exportedURL = url
                showShareSheet = true
            }
        } else {
            // print("[PlayerTopBar:Export] PDF生成失败")
            // TODO: 显示错误提示
        }
    }
}

// MARK: - Preview Provider
struct PlayerTopBarView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            PlayerTopBarView(theme: .light)
                .environmentObject(FavoriteManager())
                .environmentObject(PlayerViewModel())
                .previewDisplayName("Light Theme")

            PlayerTopBarView(theme: .dark)
                .environmentObject(FavoriteManager())
                .environmentObject(PlayerViewModel())
                .preferredColorScheme(.dark)
                .previewDisplayName("Dark Theme")
        }
        .padding()
        .background(Color.gray)
    }
}