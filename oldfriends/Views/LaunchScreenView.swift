import SwiftUI

// 能力边界:
// 1. 作为应用启动时的过渡动画视图
// 2. 提供品牌 Logo 展示和简单动画效果
// 3. 支持淡出动画过渡到主内容

struct LaunchScreenView: View {
    @State private var isAnimating = false
    @State private var titleOffset: CGFloat = -200  // 标题初始位置在屏幕左侧
    @State private var subtitleOffset: CGFloat = -200  // 副标题初始位置在屏幕左侧
    @Binding var showLaunchScreen: Bool

    var body: some View {
        ZStack {
            Color.white
                .ignoresSafeArea()

            VStack(spacing: 25) {
                Image("AppIcon")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 120, height: 120)
                    .cornerRadius(25)
                    .scaleEffect(isAnimating ? 1.0 : 0.5)

                VStack(spacing: 12) {
                    Text("Hi，老友，又见面了")
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.black)
                        .opacity(isAnimating ? 1 : 0)
                        .offset(x: titleOffset)  // 添加水平偏移

                    Text("每天进步一点点~")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.gray)
                        .opacity(isAnimating ? 1 : 0)
                        .offset(x: subtitleOffset)  // 添加水平偏移
                }
            }
        }
        .onAppear {
            // Logo 缩放动画
            withAnimation(.easeOut(duration: 1.0)) {
                isAnimating = true
            }

            // 标题从左到右动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
                titleOffset = 0
            }

            // 副标题从左到右动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4)) {
                subtitleOffset = 0
            }

            // 1秒后隐藏启动画面
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showLaunchScreen = false
                }
            }
        }
    }
}

#Preview {
    LaunchScreenView(showLaunchScreen: .constant(true))
}