import SwiftUI
import AVFoundation

struct FavoriteWordsView: View {
    @EnvironmentObject var favoriteManager: FavoriteManager
    @EnvironmentObject var viewModel: PlayerViewModel
    @Environment(\.presentationMode) var presentationMode
    private let synthesizer = AVSpeechSynthesizer()
    @State private var selectedTab = 0
    private let debugId = UUID().uuidString.prefix(6)
    @State private var isPlaying = false
    @State private var currentAudioPlayer: AVPlayer?
    @State private var currentIndex = 0
    @State private var currentPlayingIndex: Int? = nil
    @State private var showExportMenu = false
    @State private var isExporting = false
    @State private var exportedURL: URL?
    @State private var showShareSheet = false
    @State private var documentURL: URL?
    @State private var showDocumentPicker = false

    var body: some View {
        ZStack {
            ThemeColors.background(viewModel.currentTheme)
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                // Tab 切换栏
                VStack(spacing: 0) {
                    HStack(spacing: 0) {
                        TabButton(
                            title: "单词",
                            isSelected: selectedTab == 0,
                            theme: viewModel.currentTheme
                        ) {
                            withAnimation { selectedTab = 0 }
                        }

                        TabButton(
                            title: "句子",
                            isSelected: selectedTab == 1,
                            theme: viewModel.currentTheme
                        ) {
                            withAnimation { selectedTab = 1 }
                        }
                    }
                    .padding(.top, 20)
                    .padding(.bottom, 8)
                }
                .background(ThemeColors.surface(viewModel.currentTheme))
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(ThemeColors.border(viewModel.currentTheme))
                        .opacity(0.6),
                    alignment: .bottom
                )
                .shadow(color: viewModel.currentTheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)

                // 修改这里：移除 GeometryReader，直接使用 TabView
                TabView(selection: $selectedTab) {
                    // 单词列表
                    WordsList(synthesizer: synthesizer, theme: viewModel.currentTheme)
                        .tag(0)
                        //.onAppear {  print("[FavoriteWordsView:\(debugId)] 单词列表页面显示") }

                    // 句子列表
                    SentencesList(
                        viewModel: viewModel,
                        theme: viewModel.currentTheme,
                        isPlayingAll: isPlaying,
                        currentPlayingIndex: currentPlayingIndex,
                        onStopPlayingAll: {
                            // 停止全部播放
                            stopPlayback()
                        }
                    )
                        .tag(1)
                      //  .onAppear {  print("[FavoriteWordsView:\(debugId)] 句子列表页面显示") }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .onAppear {
                    // 添加页面访问埋点
                    if selectedTab == 0 {
                        AnalyticsManager.shared.logFavoriteWordsScreenView()
                    } else {
                        AnalyticsManager.shared.logFavoriteSentencesScreenView()
                    }
                }
                .onChange(of: selectedTab) { newValue in
                    // 切换标签页时也需要埋点
                    if selectedTab == 0 {
                        AnalyticsManager.shared.logFavoriteWordsScreenView()
                    } else {
                        AnalyticsManager.shared.logFavoriteSentencesScreenView()
                    }
                }
            }
            .navigationBarBackButtonHidden(true)
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(ThemeColors.surface(viewModel.currentTheme).opacity(0.95), for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                    }
                    .tint(ThemeColors.primaryText(viewModel.currentTheme))
                }

                ToolbarItem(placement: .principal) {
                    Text("收藏")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    if selectedTab == 1 {
                        Button(action: {
                            if isPlaying {
                                stopPlayback()
                            } else {
                                startPlayback()
                            }
                        }) {
                            Image(systemName: isPlaying ? "pause.circle" : "play.circle")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                        }
                    } else {
                        Button(action: {
                            showExportMenu = true
                        }) {
                            Image(systemName: "doc.badge.arrow.up")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                        }
                    }
                }
            }
        }
        .onChange(of: viewModel.currentTheme) { newValue in
            // print("FavoriteWordsView - Theme Changed to: \(viewModel.currentTheme)")
        }
        .onAppear {
            // print("[FavoriteWordsView:\(debugId)] 视图出现")
        }
        .onDisappear {
            stopPlayback()
        }
        .overlay {
            if showExportMenu {
                // 半透明背景
                Color.black.opacity(0.4)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        withAnimation {
                            showExportMenu = false
                        }
                    }

                // 底部菜单
                VStack(spacing: 0) {
                    Spacer()

                    VStack(spacing: 0) {
                        // 标题
                        Text("导出收藏")
                            .font(.system(size: 17, weight: .semibold))
                            .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                            .padding(.vertical, 16)

                        Divider()
                            .background(ThemeColors.border(viewModel.currentTheme))

                        // 导出单词列表按钮
                        Button(action: {
                            withAnimation {
                                showExportMenu = false
                            }
                            Task {
                                await exportWords()
                            }
                        }) {
                            Text("导出单词列表")
                                .font(.system(size: 17))
                                .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                        }

                        Divider()
                            .background(ThemeColors.border(viewModel.currentTheme))

                        // 导出句子列表按钮
                        Button(action: {
                            withAnimation {
                                showExportMenu = false
                            }
                            Task {
                                await exportSentences()
                            }
                        }) {
                            Text("导出句子列表")
                                .font(.system(size: 17))
                                .foregroundColor(ThemeColors.primaryText(viewModel.currentTheme))
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                        }

                        Divider()
                            .background(ThemeColors.border(viewModel.currentTheme))

                        // 取消按钮
                        Button(action: {
                            withAnimation {
                                showExportMenu = false
                            }
                        }) {
                            Text("取消")
                                .font(.system(size: 17, weight: .medium))
                                .foregroundColor(ThemeColors.primary)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                        }
                    }
                    .background(ThemeColors.surface(viewModel.currentTheme))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(ThemeColors.border(viewModel.currentTheme), lineWidth: 1)
                    )
                    .shadow(color: viewModel.currentTheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                    .padding(.horizontal, 16)
                    .padding(.bottom, 8)
                }
                .transition(.move(edge: .bottom))
            }
        }
        .animation(.spring(response: 0.3), value: showExportMenu)
        .sheet(isPresented: $showShareSheet) {
            if let url = exportedURL {
                ShareSheet(activityItems: [url])
            }
        }
        .overlay {
            if isExporting {
                ZStack {
                    Color.black.opacity(0.4)
                        .edgesIgnoringSafeArea(.all)

                    ProgressView("正在生成 PDF...")
                        .padding()
                        .background(ThemeColors.surface(viewModel.currentTheme))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(ThemeColors.border(viewModel.currentTheme), lineWidth: 1)
                        )
                        .shadow(color: viewModel.currentTheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                }
            }
        }
    }

    private func startPlayback() {
        let sentences = favoriteManager.getAllFavoriteSentences()
        guard !sentences.isEmpty else { return }

        isPlaying = true
        currentIndex = 0
        currentPlayingIndex = 0
        playCurrentSentence()
    }

    private func stopPlayback() {
        isPlaying = false
        currentAudioPlayer?.pause()
        currentAudioPlayer = nil
        currentPlayingIndex = nil
    }

    private func playCurrentSentence() {
        let sentences = favoriteManager.getAllFavoriteSentences()
        guard currentIndex < sentences.count else {
            currentIndex = 0
            currentPlayingIndex = nil
            if isPlaying {
                playCurrentSentence()
            }
            return
        }

        let sentence = sentences[currentIndex]
        currentPlayingIndex = currentIndex

        Task {
            // 1. 首先尝试从下载目录获取音频文件
            let fileManager = FileManager.default
            let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]

            // 尝试在多个可能的位置查找文件
            let possibleLocations = [
                documentsPath.appendingPathComponent("audio/\(sentence.audioFileName).mp3"),  // 新的主要路径
                documentsPath.appendingPathComponent("\(sentence.audioFileName).mp3"),        // 旧的可能路径
                documentsPath.appendingPathComponent("Downloads/\(sentence.audioFileName).mp3")
            ]

            // 2. 在所有可能的位置中查找文件
            var audioURL: URL?
            for location in possibleLocations {
                if fileManager.fileExists(atPath: location.path) &&
                   fileManager.isReadableFile(atPath: location.path) {
                    audioURL = location
                    // print("[SentenceCard] 找到可用的音频文件: \(location.path)")
                    break
                }
            }

            // 3. 如果在下载目录都没找到，尝试从 Bundle 获取
            if audioURL == nil {
                audioURL = Bundle.main.url(forResource: sentence.audioFileName, withExtension: "mp3")
                // print("[SentenceCard] 尝试从 Bundle 获取音频文件")
            }

            if let audioURL = audioURL {
                // print("[SentenceCard] 音频文件路径: \(audioURL.path)")

                await MainActor.run {
                    let playerItem = AVPlayerItem(url: audioURL)
                    let start = CMTime(seconds: sentence.startTime, preferredTimescale: 1000)
                    let duration = CMTime(seconds: sentence.endTime - sentence.startTime, preferredTimescale: 1000)
                    playerItem.forwardPlaybackEndTime = CMTimeAdd(start, duration)

                    currentAudioPlayer = AVPlayer(playerItem: playerItem)
                    currentAudioPlayer?.seek(to: start)
                    currentAudioPlayer?.play()

                    NotificationCenter.default.addObserver(
                        forName: .AVPlayerItemDidPlayToEndTime,
                        object: playerItem,
                        queue: .main
                    ) { _ in
                        Task { @MainActor in
                            if isPlaying {
                                currentIndex += 1
                                playCurrentSentence()
                            }
                        }
                    }

                    // 添加错误处理
                    NotificationCenter.default.addObserver(
                        forName: .AVPlayerItemFailedToPlayToEndTime,
                        object: playerItem,
                        queue: .main
                    ) { notification in
                        if let error = notification.userInfo?[AVPlayerItemFailedToPlayToEndTimeErrorKey] as? Error {
                             print("[SentenceCard] 播放失败: \(error)")
                        }
                    }
                }
            } else {
                // print("[SentenceCard] 未找到音频文件：\(sentence.audioFileName).mp3")
                // print("[SentenceCard] 请确保该剧集已下载或在 Bundle 中")
                // 如果当前文件找不到，尝试播放下一个
                currentIndex += 1
                playCurrentSentence()
            }
        }
    }

    private func exportWords() async {
        // 添加导出收藏单词点击埋点
        AnalyticsManager.shared.logExportFavoriteWordsClick()

        // print("[FavoriteWordsView:Export] 开始导出单词")

        await MainActor.run {
            isExporting = true
        }

        defer {
            Task { @MainActor in
                isExporting = false
            }
        }

        let wordsWithEpisodes = favoriteManager.favoriteWords.map { word -> (word: String, episode: (seasonNumber: Int, episodeNumber: Int)?) in
            if let details = favoriteManager.getWordDetails(word) {
                return (word: word, episode: (seasonNumber: details.seasonNumber, episodeNumber: details.episodeNumber))
            }
            return (word: word, episode: nil)
        }
        // print("[FavoriteWordsView:Export] 准备导出 \(wordsWithEpisodes.count) 个单词")

        if let url = await PDFGenerator.generateWordsPDF(words: wordsWithEpisodes, theme: viewModel.currentTheme) {
            // print("[FavoriteWordsView:Export] PDF生成成功，文件路径: \(url)")
            await MainActor.run {
                exportedURL = url
                showShareSheet = true
            }
        } else {
            // print("[FavoriteWordsView:Export] PDF生成失败")
        }
    }

    private func exportSentences() async {
        // 添加导出收藏句子点击埋点
        AnalyticsManager.shared.logExportFavoriteSentencesClick()

        // print("[FavoriteWordsView:Export] 开始导出句子")
        isExporting = true
        defer {
            // print("[FavoriteWordsView:Export] 导出过程结束")
            isExporting = false
        }

        let sentences = favoriteManager.getAllFavoriteSentences()
        // print("[FavoriteWordsView:Export] 准备导出 \(sentences.count) 个句子")

        if let url = await PDFGenerator.generateSentencesPDF(sentences: sentences, theme: viewModel.currentTheme) {
            // print("[FavoriteWordsView:Export] PDF生成成功，文件路径: \(url)")
            await MainActor.run {
                exportedURL = url
                showShareSheet = true
            }
        } else {
            // print("[FavoriteWordsView:Export] PDF生成失败，返回了nil")
        }
    }
}

// Tab 按钮组件
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let theme: AppTheme
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.system(size: 16, weight: isSelected ? .semibold : .regular))
                    .foregroundColor(isSelected ?
                        ThemeColors.primary :
                        ThemeColors.secondaryText(theme))

                // 下划线
                Rectangle()
                    .fill(isSelected ? ThemeColors.primary : Color.clear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 16)
    }
}

// 词列表组件
struct WordsList: View {
    @EnvironmentObject private var favoriteManager: FavoriteManager
    let synthesizer: AVSpeechSynthesizer
    let theme: AppTheme
    @State private var wordToDelete: String? = nil
    private let debugId = UUID().uuidString.prefix(6)

    var body: some View {
        let words: [String] = Array(favoriteManager.favoriteWords).sorted()

        if words.isEmpty {
            VStack(spacing: 16) {
                Spacer()
                    .frame(height: 80)

                HStack(spacing: 12) {
                    Image(systemName: "star.circle")
                        .font(.system(size: 32))
                        .foregroundColor(ThemeColors.primary)

                    Text("还没有收藏单词")
                        .font(.system(size: 17, weight: .medium))
                        .foregroundColor(ThemeColors.primaryText(theme))
                }

                Text("点击字幕中的单词，在弹出的气泡中点击收藏按钮即可收藏")
                    .font(.system(size: 15))
                    .foregroundColor(ThemeColors.secondaryText(theme))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)

                Spacer()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(ThemeColors.surface(theme).opacity(0.95))
        } else {
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(words, id: \.self) { (word: String) in
                        SwipeableWordCard(word: word, synthesizer: synthesizer, theme: theme) {
                            // print("[WordsList:\(debugId)] 删除单词: \(word)")
                            favoriteManager.toggleWordFavorite(word)
                        }
                        .padding(.horizontal, 16)
                    }
                }
                .padding(.vertical, 12)
            }
            .background(Color.clear)
        }
    }
}

// 可滑动删除的单词卡片组件
struct SwipeableWordCard: View {
    let word: String
    let synthesizer: AVSpeechSynthesizer
    let theme: AppTheme
    let onDelete: () -> Void

    @State private var offset: CGFloat = 0
    @State private var isSwiped = false
    @State private var showDelete = false
    @State private var autoResetTimer: Timer? = nil

    private func resetCard() {
        withAnimation(.interactiveSpring()) {
            showDelete = false
            offset = 0
            isSwiped = false
        }
    }

    private func startAutoResetTimer() {
        // 取消现有的定时器
        autoResetTimer?.invalidate()
        // 创建新的定时器，3秒后自动回弹
        autoResetTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            DispatchQueue.main.async {
                resetCard()
            }
        }
    }

    private func cancelAutoResetTimer() {
        autoResetTimer?.invalidate()
        autoResetTimer = nil
    }

    var body: some View {
        ZStack(alignment: .trailing) {
            // 删除按钮背景
            if showDelete {
                Button(action: {
                    cancelAutoResetTimer()
                    deleteWithAnimation()
                }) {
                    Label("删除", systemImage: "trash")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
                .frame(width: 100)
                .frame(maxHeight: .infinity)
                .background(ThemeColors.error)
                .cornerRadius(12)
                .padding(.leading, 20)
                .transition(.move(edge: .trailing))
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
            }

            // 单词卡片
            WordCard(word: word, synthesizer: synthesizer, theme: theme)
                .offset(x: offset)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            cancelAutoResetTimer()
                            withAnimation(.interactiveSpring()) {
                                // 限制只能向左滑动，且最大滑动距离为100
                                offset = max(min(0, value.translation.width), -100)
                                showDelete = offset < -20
                            }
                        }
                        .onEnded { value in
                            if value.translation.width < -50 {
                                // 滑动超过阈值，保持打开状态
                                withAnimation(.interactiveSpring()) {
                                    offset = -100
                                    showDelete = true
                                    isSwiped = true
                                }
                                // 开始自动回弹计时
                                startAutoResetTimer()
                            } else {
                                resetCard()
                            }
                        }
                )
                .onTapGesture {
                    if isSwiped {
                        cancelAutoResetTimer()
                        resetCard()
                    }
                }
        }
    }

    private func deleteWithAnimation() {
        withAnimation(.easeOut(duration: 0.2)) {
            offset = -UIScreen.main.bounds.width
        }

        // 延迟一小段时间后执行实际的删除操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            onDelete()
        }
    }
}

// 修改句子列表组件
struct SentencesList: View {
    @EnvironmentObject var favoriteManager: FavoriteManager
    let viewModel: PlayerViewModel
    let theme: AppTheme
    let isPlayingAll: Bool
    let currentPlayingIndex: Int?
    private let debugId = UUID().uuidString.prefix(6)
    @State private var currentlyPlayingId: String? = nil
    var onStopPlayingAll: (() -> Void)?

    var body: some View {
        let sentences = favoriteManager.getAllFavoriteSentences()

        if sentences.isEmpty {
            VStack(spacing: 16) {
                Spacer()
                    .frame(height: 80)

                HStack(spacing: 12) {
                    Image(systemName: "text.badge.star")
                        .font(.system(size: 32))
                        .foregroundColor(ThemeColors.primary)

                    Text("还没有收藏句子")
                        .font(.system(size: 17, weight: .medium))
                        .foregroundColor(ThemeColors.primaryText(theme))
                }

                Text("在播放页面左滑字幕，点击收藏按钮即可收藏句子")
                    .font(.system(size: 15))
                    .foregroundColor(ThemeColors.secondaryText(theme))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)

                Spacer()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(ThemeColors.surface(theme).opacity(0.95))
        } else {
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(0..<sentences.count, id: \.self) { index in
                        let sentence = sentences[index]
                        sentenceCardView(sentence: sentence, index: index)
                    }
                }
                .padding(.vertical, 12)
            }
            .background(Color.clear)
        }
    }

    private func sentenceCardView(sentence: FavoriteSentence, index: Int) -> some View {
        let isPlaying = isPlayingAll ? currentPlayingIndex == index : currentlyPlayingId == sentence.english

        return SwipeableSentenceCard(
            sentence: sentence,
            theme: theme,
            isCurrentlyPlaying: isPlaying,
            onPlayButtonTapped: {
                handlePlayButtonTapped(for: sentence)
            },
            onDelete: {
                favoriteManager.toggleSentenceFavorite(
                    english: sentence.english,
                    chinese: sentence.chinese,
                    seasonNumber: sentence.seasonNumber,
                    episodeNumber: sentence.episodeNumber,
                    episodeTitle: sentence.episodeTitle,
                    timestamp: sentence.timestamp,
                    startTime: sentence.startTime,
                    endTime: sentence.endTime,
                    audioFileName: sentence.audioFileName
                )
            }
        )
        .padding(.horizontal, 16)
    }

    private func handlePlayButtonTapped(for sentence: FavoriteSentence) {
        if isPlayingAll {
            onStopPlayingAll?()
        }
        if currentlyPlayingId == sentence.english {
            currentlyPlayingId = nil
        } else {
            currentlyPlayingId = sentence.english
        }
    }
}

// 可滑动删除的句子卡片组件
struct SwipeableSentenceCard: View {
    let sentence: FavoriteSentence
    let theme: AppTheme
    let isCurrentlyPlaying: Bool
    let onPlayButtonTapped: () -> Void
    let onDelete: () -> Void

    @State private var offset: CGFloat = 0
    @State private var isSwiped = false
    @State private var showDelete = false
    @State private var autoResetTimer: Timer? = nil

    private func resetCard() {
        withAnimation(.interactiveSpring()) {
            showDelete = false
            offset = 0
            isSwiped = false
        }
    }

    private func startAutoResetTimer() {
        autoResetTimer?.invalidate()
        autoResetTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            DispatchQueue.main.async {
                resetCard()
            }
        }
    }

    private func cancelAutoResetTimer() {
        autoResetTimer?.invalidate()
        autoResetTimer = nil
    }

    var body: some View {
        ZStack(alignment: .trailing) {
            // 删除按钮背景
            if showDelete {
                Button(action: {
                    cancelAutoResetTimer()
                    deleteWithAnimation()
                }) {
                    Label("删除", systemImage: "trash")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
                .frame(width: 100)
                .frame(maxHeight: .infinity)
                .background(ThemeColors.error)
                .cornerRadius(12)
                .padding(.leading, 20)
                .transition(.move(edge: .trailing))
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
            }

            // 句子卡片
            SentenceCard(
                sentence: sentence,
                theme: theme,
                isCurrentlyPlaying: isCurrentlyPlaying,
                onPlayButtonTapped: onPlayButtonTapped
            )
            .offset(x: offset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        cancelAutoResetTimer()
                        withAnimation(.interactiveSpring()) {
                            offset = max(min(0, value.translation.width), -100)
                            showDelete = offset < -20
                        }
                    }
                    .onEnded { value in
                        if value.translation.width < -50 {
                            withAnimation(.interactiveSpring()) {
                                offset = -100
                                showDelete = true
                                isSwiped = true
                            }
                            startAutoResetTimer()
                        } else {
                            resetCard()
                        }
                    }
            )
            .onTapGesture {
                if isSwiped {
                    cancelAutoResetTimer()
                    resetCard()
                }
            }
        }
    }

    private func deleteWithAnimation() {
        withAnimation(.easeOut(duration: 0.2)) {
            offset = -UIScreen.main.bounds.width
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            onDelete()
        }
    }
}

// 修改 SentenceCard 组件的样式
struct SentenceCard: View {
    let sentence: FavoriteSentence
    let theme: AppTheme
    let isCurrentlyPlaying: Bool
    let onPlayButtonTapped: () -> Void
    @EnvironmentObject var favoriteManager: FavoriteManager
    @State private var audioPlayer: AVPlayer?
    @State private var isAnimating = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .center, spacing: 12) {
                // 左侧：句子内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(sentence.english)
                        .font(.system(size: 16))
                        .foregroundColor(ThemeColors.primaryText(theme))
                        .lineSpacing(4)
                        .fixedSize(horizontal: false, vertical: true)

                    Text(sentence.chinese)
                        .font(.system(size: 14))
                        .foregroundColor(ThemeColors.secondaryText(theme))
                        .lineSpacing(4)
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()

                // 右侧：播放按钮和剧集信息
                HStack(spacing: 12) {
                    // 剧集信息
                    HStack(spacing: 4) {
                        Image(systemName: "film")
                            .font(.system(size: 10))
                            .foregroundColor(ThemeColors.secondaryText(theme))

                        Text("S\(sentence.seasonNumber)E\(sentence.episodeNumber)")
                            .font(.system(size: 12))
                            .foregroundColor(ThemeColors.secondaryText(theme))
                    }

                    // 修改播放按钮部分 - 更新样式与主播放页面一致
                    Button(action: {
                        if isCurrentlyPlaying {
                            stopPlaying()
                            onPlayButtonTapped() // 通知父视图停止播放
                        } else {
                            onPlayButtonTapped()
                            playAudio()
                        }
                    }) {
                        ZStack {
                            Circle()
                                .fill(ThemeColors.primary.opacity(0.1))
                                .frame(width: 34, height: 34)

                            if isCurrentlyPlaying {
                                Image(systemName: "pause.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(ThemeColors.primary)
                            } else {
                                Image(systemName: "play.fill")
                                    .font(.system(size: 16))
                                    .foregroundColor(ThemeColors.primary)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .frame(minHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(theme == .dark ? Color.black : .white)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(ThemeColors.border(theme), lineWidth: 1)
        )
        .shadow(color: theme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
        .onChange(of: isCurrentlyPlaying) { newValue in
            if isCurrentlyPlaying {
                // 开始播放时启动动画
                withAnimation {
                    isAnimating = true
                }
            } else {
                // 停止播放时停止动画
                withAnimation {
                    isAnimating = false
                }
                stopPlaying()
            }
        }
    }

    // 修改音频播放功能
    private func playAudio() {
        Task {
            // 1. 首先尝试从下载目录获取音频文件
            let fileManager = FileManager.default
            let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]

            // 检查 Documents 目录是否存在
            var isDirectory: ObjCBool = false
            let _ = fileManager.fileExists(atPath: documentsPath.path, isDirectory: &isDirectory)
            // print("[SentenceCard] Documents目录状态 - 是目录: \(isDirectory.boolValue)")

            // 尝试在多个可能的位置查找文件，注意这里修改了路径
            let possibleLocations = [
                documentsPath.appendingPathComponent("audio/\(sentence.audioFileName).mp3"),  // 新的主要路径
                documentsPath.appendingPathComponent("\(sentence.audioFileName).mp3"),        // 旧的可能路径
                documentsPath.appendingPathComponent("Downloads/\(sentence.audioFileName).mp3")
            ]

            // print("[SentenceCard] 正在检查以下位置:")
            possibleLocations.forEach { url in
                // print("- \(url.path)")
                if fileManager.fileExists(atPath: url.path) {
                    // print("  ✓ 文件存在")
                    // 检查文件权限
                    if fileManager.isReadableFile(atPath: url.path) {
                        // print("  ✓ 文件可读")
                    } else {
                        // print("  × 文件不可读")
                    }
                } else {
                    // print("  × 文件不存在")
                }
            }

            // 2. 在所有可能的位置中查找文件
            var audioURL: URL?
            for location in possibleLocations {
                if fileManager.fileExists(atPath: location.path) &&
                   fileManager.isReadableFile(atPath: location.path) {
                    audioURL = location
                    // print("[SentenceCard] 找到可用的音频文件: \(location.path)")
                    break
                }
            }

            // 3. 如果在下载目录都没找到，尝试从 Bundle 获取
            if audioURL == nil {
                audioURL = Bundle.main.url(forResource: sentence.audioFileName, withExtension: "mp3")
                // print("[SentenceCard] 尝试从 Bundle 获取音频文件")
            }

            if let audioURL = audioURL {
                // print("[SentenceCard] 音频文件路径: \(audioURL.path)")

                do {
                    // 尝试读取文件属性
                    let _ = try fileManager.attributesOfItem(atPath: audioURL.path)
                    // print("[SentenceCard] 文件大小: \(attrs[.size] ?? 0) bytes")
                    // print("[SentenceCard] 文件权限: \(attrs[.posixPermissions] ?? 0)")
                } catch {
                    // print("[SentenceCard] 无法读取文件属性: \(error)")
                }

                await MainActor.run {
                    // 4. 创建 AVPlayerItem
                    let playerItem = AVPlayerItem(url: audioURL)

                    // 5. 设置播放时间范围
                    let start = CMTime(seconds: sentence.startTime, preferredTimescale: 1000)
                    let duration = CMTime(seconds: sentence.endTime - sentence.startTime, preferredTimescale: 1000)

                    // 6. 设置播放范围
                    playerItem.forwardPlaybackEndTime = CMTimeAdd(start, duration)

                    // 7. 创建或重用播放器
                    if audioPlayer == nil {
                        audioPlayer = AVPlayer(playerItem: playerItem)
                    } else {
                        audioPlayer?.replaceCurrentItem(with: playerItem)
                    }

                    // 8. 设置起始位置并播放
                    audioPlayer?.seek(to: start)

                    // print("[SentenceCard] 开始播放音频片段: \(sentence.audioFileName)")
                    // print("[SentenceCard] 开始时间: \(sentence.startTime), 结束时间: \(sentence.endTime)")

                    // 添加播放结束的观察者
                    NotificationCenter.default.addObserver(
                        forName: .AVPlayerItemDidPlayToEndTime,
                        object: audioPlayer?.currentItem,
                        queue: .main
                    ) { _ in
                        Task { @MainActor in
                            onPlayButtonTapped() // 播放结束时通知父视图
                        }
                    }

                    // 添加错误处理
                    NotificationCenter.default.addObserver(
                        forName: .AVPlayerItemFailedToPlayToEndTime,
                        object: audioPlayer?.currentItem,
                        queue: .main
                    ) { notification in
                        if let _ = notification.userInfo?[AVPlayerItemFailedToPlayToEndTimeErrorKey] as? Error {
                            // print("[SentenceCard] 播放失败: \(error)")
                        }
                    }

                    // 开始播放
                    audioPlayer?.play()
                }
            } else {
                // print("[SentenceCard] 未找到音频文件：\(sentence.audioFileName).mp3")
                // print("[SentenceCard] 请确保该剧集已下载或在 Bundle 中")
            }
        }
    }

    // 修改停止播放的方法
    private func stopPlaying() {
        audioPlayer?.pause()
        audioPlayer?.seek(to: .zero)
        audioPlayer = nil
        // 移除播放结束的观察者
        NotificationCenter.default.removeObserver(self,
            name: .AVPlayerItemDidPlayToEndTime,
            object: audioPlayer?.currentItem)
    }
}

// 修改单词卡组件 - 移除左滑删功能
struct WordCard: View {
    let word: String
    let synthesizer: AVSpeechSynthesizer
    let theme: AppTheme
    @EnvironmentObject var favoriteManager: FavoriteManager
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .center, spacing: 12) {
                // 左侧：单词和音标部分
                VStack(alignment: .leading, spacing: 4) {
                    Text(word)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(ThemeColors.primaryText(theme))

                    if let entry = DictionaryDatabase.shared.lookup(word),
                       let phonetic = entry.phonetic {
                        Text("[\(phonetic)]")
                            .font(.system(size: 14))
                            .foregroundColor(ThemeColors.secondaryText(theme))
                    }
                }

                Spacer()



                // 右侧：发音按钮和剧集信息
                HStack(spacing: 12) {
                    // 剧集信息
                    if let details = favoriteManager.getWordDetails(word) {
                        HStack(spacing: 4) {
                            Image(systemName: "film")
                                .font(.system(size: 10))
                                .foregroundColor(ThemeColors.secondaryText(theme))

                            Text("S\(details.seasonNumber)E\(details.episodeNumber)")
                                .font(.system(size: 12))
                                .foregroundColor(ThemeColors.secondaryText(theme))
                        }
                    }

                    // 发音按钮 - 更新样式与主播放页面一致
                    Button(action: {
                        speakWord(word)
                    }) {
                        ZStack {
                            Circle()
                                .fill(ThemeColors.primary.opacity(0.1))
                                .frame(width: 34, height: 34)

                            Image(systemName: "speaker.wave.2")
                                .font(.system(size: 16))
                                .foregroundColor(ThemeColors.primary)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 12)

            // 修改词义部分
            if let entry = DictionaryDatabase.shared.lookup(word),
               let translation = entry.translation {
                let lines = translation.components(separatedBy: "\\n")

                VStack(alignment: .leading, spacing: 4) {
                    // 始终显示第一行
                    if let firstLine = lines.first {
                        Text(firstLine)
                            .font(.system(size: 15))
                            .foregroundColor(ThemeColors.secondaryText(theme))
                            .lineLimit(1)
                    }

                    // 如果有多行且已展开，显示剩余行
                    if lines.count > 1 {
                        if isExpanded {
                            ForEach(lines.dropFirst(), id: \.self) { line in
                                Text(line)
                                    .font(.system(size: 15))
                                    .foregroundColor(ThemeColors.secondaryText(theme))
                                    .lineSpacing(4)
                            }
                        }

                        // 展开/收起按钮
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isExpanded.toggle()
                            }
                        }) {
                            HStack(spacing: 4) {
                                Text(isExpanded ? "收起" : "更多释义")
                                    .font(.system(size: 14))
                                    .foregroundColor(ThemeColors.primary)

                                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                    .font(.system(size: 12))
                                    .foregroundColor(ThemeColors.primary)
                            }
                        }
                        .padding(.top, 4)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
                .padding(.bottom, 12)
            }
        }
        .frame(minHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(theme == .dark ? Color.black : .white)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(ThemeColors.border(theme), lineWidth: 1)
        )
        .shadow(color: theme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
    }

    // 保留发音功能
    private func speakWord(_ word: String) {
        let utterance = AVSpeechUtterance(string: word)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0

        synthesizer.stopSpeaking(at: .immediate)
        synthesizer.speak(utterance)
    }
}

// 新增 DocumentPicker 视图
struct DocumentPicker: UIViewControllerRepresentable {
    let url: URL

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forExporting: [url])
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
}
