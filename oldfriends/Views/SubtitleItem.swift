import SwiftUI

struct SubtitleItem: View {
    let subtitle: Subtitle
    let isHighlighted: Bool
    @ObservedObject var viewModel: PlayerViewModel
    @EnvironmentObject var wordSelectionManager: WordSelectionManager
    @EnvironmentObject var favoriteManager: FavoriteManager
    private let dictionaryDB = DictionaryDatabase.shared
    @State private var offset: CGFloat = 0
    @State private var isSwiped: Bool = false

    // 加自动弹回的计时器
    @State private var autoResetTimer: Timer?
    private let autoResetDelay: TimeInterval = 2.0 // 2秒后自动弹回

    // 添加收藏状态动画
    @State private var favoriteScale: CGFloat = 1.0

    // 修改 SubtitleItem 中的 abLoopRangeIndicator
    private var abLoopRangeIndicator: some View {
        Group {
            if let startTime = viewModel.abLoopStartTime {
                if subtitle.startTime == startTime {
                    // A 点样式
                    ZStack(alignment: .topLeading) {
                        // 字幕边框
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange, lineWidth: 2)

                        // A 点标记，位于字幕框上方
                        Text("A")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.orange)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.orange.opacity(0.2))
                            )
                            .offset(y: -20) // 向上偏移，避免重叠
                    }
                } else if subtitle.endTime < startTime {
                    // A 点之前的字幕样式（灰色禁用状态）
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                } else if let endTime = viewModel.abLoopEndTime {
                    if subtitle.endTime == endTime {
                        // B 点样式
                        ZStack(alignment: .topLeading) {
                            // 字幕边框
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.orange, lineWidth: 2)

                            // B 点标记，位于字幕框上方
                            Text("B")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.orange)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color.orange.opacity(0.2))
                                )
                                .offset(y: -20) // 向上偏移，避免重叠
                        }
                    } else if subtitle.startTime > startTime && subtitle.endTime < endTime {
                        // AB 循环区域内的样式（已设置完成）
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange, lineWidth: 1)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.orange.opacity(0.05))
                            )
                    } else if subtitle.startTime > endTime {
                        // B 点之后的字幕样式（灰色禁用状态）
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    }
                } else if viewModel.abLoopState == .settingB && subtitle.startTime > startTime {
                    // 设置 B 点时，A 点之后的可选择区域样式（虚线边框）
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            Color.orange.opacity(0.6),
                            style: StrokeStyle(
                                lineWidth: 1,
                                lineCap: .round,
                                lineJoin: .round,
                                dash: [4, 4],
                                dashPhase: 5
                            )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange.opacity(0.05))
                        )
                }
            }
        }
    }

    // 添加 AB 循环提示指示器
    private var abLoopIndicator: some View {
        Group {
            if viewModel.playMode == .abLoop {
                HStack(spacing: 4) {
                    if let prompt = viewModel.abLoopPrompt,
                       (viewModel.abLoopStartTime == nil ||
                        (viewModel.abLoopStartTime != nil && subtitle.startTime > viewModel.abLoopStartTime!)) {
                        Text(prompt)
                            .font(.system(size: 13))
                            .foregroundColor(.orange)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.orange.opacity(0.1)))
                    }
                }
            }
        }
    }

    // 在 body 属性之前添加一个辅助视图
    private var bodyContent: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 移除 index == "0" 的特殊处理，直接显示主内容
            mainContent

            // AB 循环指示器
            if viewModel.playMode == .abLoop {
                abLoopIndicator
            }
        }
        .padding(.top, 12) // 增加顶部间距
        .padding(.bottom, 12) // 增加底部间距
        .padding(.horizontal, 16)
        .frame(width: UIScreen.main.bounds.width) // 设置固定宽度为屏幕宽度
    }

    // 修改 body 属性
    var body: some View {
        bodyContent
            .onDisappear {
                autoResetTimer?.invalidate()
                autoResetTimer = nil
            }
    }

    // 主要内容视图
    private var mainContent: some View {
        Group {
            if subtitle.index != "0" {  // 只显示非0号字幕
                ZStack {
                    favoriteButtonBackground
                    mainContentStack
                }
                .frame(maxWidth: .infinity)
                .clipped()
                // 使用 simultaneousGesture 和 onTapGesture 组合
                .simultaneousGesture(
                    DragGesture(minimumDistance: GestureConstants.minimumGestureDistance)
                        .onChanged { gesture in
                            // 只有当总位移超过最小阈值时才处理
                            let totalTranslation = sqrt(
                                pow(gesture.translation.width, 2) +
                                pow(gesture.translation.height, 2)
                            )

                            if totalTranslation < GestureConstants.minimumGestureDistance {
                                return
                            }

                            // AB循环模式判断保持不变
                            if viewModel.playMode == .abLoop &&
                               (viewModel.abLoopState == .settingA || viewModel.abLoopState == .settingB) {
                                return
                            }

                            // 计算手势角度
                            let angle = abs(atan2(gesture.translation.height, gesture.translation.width))
                            let isHorizontalEnough = angle < GestureConstants.angleThreshold

                            // 使用更严格的水平判定
                            let horizontalRatio = abs(gesture.translation.width) / max(abs(gesture.translation.height), 1.0)
                            let isHorizontal = abs(gesture.translation.width) > GestureConstants.horizontalThreshold &&
                                             horizontalRatio > 4.0 && // 要求水平分量至少是垂直分量的4倍
                                             isHorizontalEnough &&
                                             abs(gesture.velocity.width) > GestureConstants.velocityThreshold

                            if isHorizontal {
                                handleDragChange(gesture)
                            } else if abs(gesture.translation.height) > GestureConstants.verticalThreshold {
                                // 如果是垂直滑动且有水平偏移，重置状态
                                if offset != 0 {
                                    withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.7)) {
                                        offset = 0
                                    }
                                }
                            }
                        }
                        .onEnded { gesture in
                            // 同样使用阈值判断
                            let isSignificantGesture = abs(gesture.translation.width) > GestureConstants.swipeThreshold ||
                                                     abs(gesture.velocity.width) > GestureConstants.velocityThreshold

                            if !isSignificantGesture {
                                resetSwipeState()
                                return
                            }

                            // AB循环模式判断保持不变
                            if viewModel.playMode == .abLoop &&
                               (viewModel.abLoopState == .settingA || viewModel.abLoopState == .settingB) {
                                return
                            }

                            handleDragEnd(gesture)
                        }
                )
                // 添加点击手势
                .onTapGesture {
                    // print("[SubtitleItem] 点击事件触发")
                    handleSubtitleTap()
                }
                .allowsHitTesting(true)
            }
        }
    }

    // 收藏按钮背景
    private var favoriteButtonBackground: some View {
        Group {
            if offset < 0 {
                HStack(spacing: 0) {
                    Spacer()
                    Rectangle()
                        .fill(ThemeColors.primary)
                        .frame(width: -offset)
                        .overlay(
                            Image(systemName: favoriteManager.isSentenceFavorited(subtitle.english) ? "star.fill" : "star")
                                .font(.system(size: 16))
                                .foregroundColor(.white)
                                .frame(width: -offset)
                                .scaleEffect(favoriteScale)
                        )
                        .onTapGesture {
                            handleFavoriteAction()
                        }
                }
            }
        }
    }

    // 主要内容堆栈 - 增加间距改善可读性
    private var mainContentStack: some View {
        VStack(spacing: 8) { // 增加英文和中文之间的间距
            if viewModel.subtitleDisplayMode == .englishOnly || viewModel.subtitleDisplayMode == .both {
                TappableTextView(
                    text: subtitle.english,
                    onWordTapped: { word, frame in
                        // 在 AB 循环设置阶段，不触发查词功能
                        if viewModel.playMode == .abLoop &&
                           (viewModel.abLoopState == .settingA ||
                            viewModel.abLoopState == .settingB) {
                            handleSubtitleTap()  // 直接处理为字幕点击
                        } else {
                            handleWordTap(word, frame)  // 正常处理单词点击
                        }
                    },
                    onWordLongPressed: { _, _ in },
                    isHighlighted: isHighlighted,
                    theme: viewModel.currentTheme,
                    selectedWord: wordSelectionManager.selectedWord?.word,
                    wordSelectionManager: wordSelectionManager
                )
                .allowsHitTesting(true)  // 始终允许点击
                .opacity(isDisabledForABLoop ? 0.5 : 1.0)
                .frame(width: UIScreen.main.bounds.width - 32, alignment: .leading) // 确保有固定宽度并左对齐
            }

            if viewModel.subtitleDisplayMode == .chineseOnly || viewModel.subtitleDisplayMode == .both {
                chineseText
                    .opacity(isDisabledForABLoop ? 0.5 : 1.0)
            }

            // 添加更优雅的分隔符
            if let currentIndex = viewModel.subtitles.firstIndex(where: { $0.id == subtitle.id }),
               currentIndex < viewModel.subtitles.count - 1 {
                Rectangle()
                    .fill(ThemeColors.border(viewModel.currentTheme).opacity(0.3))
                    .frame(width: UIScreen.main.bounds.width - 32, height: 0.5)
                    .padding(.top, 12)
            }
        }
        .background(
            Group {
                if isHighlighted {
                    // 使用更现代的高亮效果
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    ThemeColors.primary.opacity(0.08),
                                    ThemeColors.primary.opacity(0.04)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(ThemeColors.primary.opacity(0.2), lineWidth: 1)
                        )
                        .frame(width: UIScreen.main.bounds.width - 16)
                        .shadow(
                            color: ThemeColors.primary.opacity(0.1),
                            radius: 4,
                            x: 0,
                            y: 2
                        )
                }
            }
        )
        .offset(x: offset)
        .contextMenu {
            HStack(spacing: 0) {
                // 收藏按钮
                Button(action: {
                    handleFavoriteAction()
                }) {
                    HStack {
                        Image(systemName: favoriteManager.isSentenceFavorited(subtitle.english) ? "star.fill" : "star")
                            .font(.system(size: 16))
                        Text(favoriteManager.isSentenceFavorited(subtitle.english) ? "取消收藏" : "收藏")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(ThemeColors.primary)
                    .foregroundColor(.white)
                }

                // 播放按钮
                Button(action: {
                    viewModel.playSubtitle(subtitle)
                }) {
                    HStack {
                        Image(systemName: "play.fill")
                            .font(.system(size: 16))
                        Text("播放")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(ThemeColors.primary)
                    .foregroundColor(.white)
                }
            }
            .frame(width: 200)
            .cornerRadius(8)
        }
    }

    // 中文文本视图 - 调整为辅助信息的视觉层次
    private var chineseText: some View {
        Text(subtitle.chinese)
            .font(.system(size: 15, weight: .regular)) // 减小字号，使用常规字重
            .foregroundColor(isHighlighted ? ThemeColors.primary : ThemeColors.secondaryText(viewModel.currentTheme)) // 使用次要文本色
            .padding(.vertical, 4) // 增加垂直间距
            .fixedSize(horizontal: false, vertical: true)
            .lineLimit(nil)
            .multilineTextAlignment(.leading)  // 左对齐
            .frame(width: UIScreen.main.bounds.width - 32, alignment: .leading)  // 设置固定宽度并左对齐
    }

    // 英文本视图
    private var englishText: some View {
        let content = subtitle.english

        return TappableTextView(
            text: content,
            onWordTapped: handleWordTap,
            onWordLongPressed: { _, _ in },
            isHighlighted: isHighlighted,
            theme: viewModel.currentTheme,
            selectedWord: wordSelectionManager.selectedWord?.word,
            wordSelectionManager: wordSelectionManager
        )
        .allowsHitTesting(!(viewModel.playMode == .abLoop &&
                           (viewModel.abLoopState == .settingA ||
                            viewModel.abLoopState == .settingB)))
        .frame(maxWidth: .infinity)
        .fixedSize(horizontal: false, vertical: true)
    }

    // 添加手势处理方法
    private var swipeGesture: some Gesture {
        DragGesture()
            .onChanged { gesture in
                handleDragChange(gesture)
            }
            .onEnded { gesture in
                handleDragEnd(gesture)
            }
    }

    // 添加手势点击处理方法
    private func handleDragChange(_ gesture: DragGesture.Value) {
        if abs(offset - gesture.translation.width) > 10 {
            // print("[SubtitleItem] 处理拖动变化: \(gesture.translation.width)")
        }

        autoResetTimer?.invalidate()
        autoResetTimer = nil

        withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.7)) {
            if gesture.translation.width < 0 {
                // 添加渐进式阻尼
                let dampedOffset = -80 * (1 - exp(-abs(gesture.translation.width) / 100))
                offset = max(dampedOffset, -80)
            } else if offset != 0 {
                // 右滑动添加阻尼
                offset = min(20 * (1 - exp(-gesture.translation.width / 50)), 20)
            }
        }
    }

    private func handleDragEnd(_ gesture: DragGesture.Value) {
        let velocity = gesture.velocity.width
        let translation = gesture.translation.width

        // 综合考虑位移和速度
        let isSignificantSwipe = abs(translation) > GestureConstants.swipeThreshold ||
                                abs(velocity) > GestureConstants.velocityThreshold

        if translation < -GestureConstants.swipeThreshold && isSignificantSwipe {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                offset = -80
                isSwiped = true
            }
            startAutoResetTimer()
        } else {
            resetSwipeState()
        }
    }

    private func handleSubtitleTap() {
        // print("[SubtitleItem] 处理字幕点击 - 当前播放模式: \(viewModel.playMode)")

        // AB循环模式判断保持不变
        if viewModel.playMode == .abLoop {
            // 如果是设置阶段（settingA 或 settingB），处理 AB 点设置
            if viewModel.abLoopState == .settingA || viewModel.abLoopState == .settingB {
                // print("[SubtitleItem] AB循环设置阶段 - 设置循环点")
                // 如果是 AB 循环模式且字幕在 A 点之前，禁止点击
                if let startTime = viewModel.abLoopStartTime {
                    if subtitle.endTime < startTime {
                        // print("[SubtitleItem] AB循环模式 - 点击被禁止：字幕在A点之前")
                        return
                    }
                }
                viewModel.setABLoopPoint(subtitle)
            } else if viewModel.abLoopState == .looping {
                // 在循环播放阶段，允许单词查询
                // print("[SubtitleItem] AB循环播放阶段 - 处理单词点击")
                if wordSelectionManager.selectedWord != nil {
                    // print("[SubtitleItem] 清除选中单词")
                    wordSelectionManager.selectedWord = nil
                } else {
                    // 不触发字幕播放，只允许单词查询
                    // print("[SubtitleItem] 允许单词查询")
                }
            }
        } else {
            // 非 AB 循环模式下的原有逻辑
            if isSwiped {
                // print("[SubtitleItem] 重置滑动状态")
                resetSwipeState()
            } else if offset < 0 {
                // print("[SubtitleItem] 重置偏移")
                resetSwipeState()
            } else {
                // 如果有查词气泡，先关闭气泡
                if wordSelectionManager.selectedWord != nil {
                    // print("[SubtitleItem] 清除选中单词")
                    wordSelectionManager.selectedWord = nil
                    // 如果是暂停状态，恢复播放
                    if !viewModel.isPlaying {
                        viewModel.togglePlayPause()
                    }
                } else if viewModel.canPlaySubtitle() {
                    // print("[SubtitleItem] 播放字幕")
                    // 关闭查词气泡
                    wordSelectionManager.selectedWord = nil
                    viewModel.playSubtitle(subtitle)
                }
            }
        }
    }
    private func handleWordTap(_ wordInfo: String, _ frame: CGRect) {
        // 如果是空字符串，说明点击了非单词区域，关闭气泡
        if wordInfo.isEmpty {
            print("[SubtitleItem] 点击非单词区域，关闭查词气泡")
            wordSelectionManager.selectedWord = nil
            return
        }

        let components = wordInfo.split(separator: "|")
        guard components.count == 2 else {
            print("[SubtitleItem] 单词信息格式错误: \(wordInfo)")
            return
        }

        let lookupWord = String(components[0])  // 原型，用于查询
        let displayWord = String(components[1]) // 显示形式

        print("[SubtitleItem] 处理单词点击: 查询=\(lookupWord), 显示=\(displayWord), 位置=\(frame)")

        // 暂停播放
        if viewModel.isPlaying {
            viewModel.togglePlayPause()
        }

        // 触发查词
        wordSelectionManager.selectedWord = (lookupWord, frame)
        print("[SubtitleItem] 已设置selectedWord: \(lookupWord)")
    }

    // 新增：提取缩写词的主部分
    private func extractBaseWord(_ word: String) -> String {
        // 常见的缩写形处理
        if word.contains("'") {
            // 处理 's, 't, 've, 'll, 're 等常见缩写
            if word.hasSuffix("'s") {
                return String(word.dropLast(2))
            } else if word.hasSuffix("'t") ||
                      word.hasSuffix("'m") ||
                      word.hasSuffix("'d") {
                return String(word.dropLast(2))
            } else if word.hasSuffix("'ve") ||
                      word.hasSuffix("'ll") ||
                      word.hasSuffix("'re") {
                return String(word.dropLast(3))
            }
        }
        return word
    }

    private func handleFavoriteAction() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            favoriteScale = 0.5
        }

        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()

        favoriteManager.toggleSentenceFavorite(
            english: subtitle.english,
            chinese: subtitle.chinese,
            seasonNumber: viewModel.currentSeasonNumber,
            episodeNumber: viewModel.currentEpisodeNumber,
            episodeTitle: viewModel.currentEpisodeTitle,
            timestamp: Date(),
            startTime: subtitle.startTime,
            endTime: subtitle.endTime,
            audioFileName: viewModel.currentAudioName
        )

        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            favoriteScale = 1.2
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                favoriteScale = 1.0
            }
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            resetSwipeState()
        }
    }

    // 修重置状态的动画
    private func resetSwipeState() {
        // print("""
//        [SubtitleItem] 重置滑动状态:
//        - 当前偏移: \(offset)
//        - 当前是否滑出: \(isSwiped)
//        """)

        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            offset = 0
            isSwiped = false
        }
        autoResetTimer?.invalidate()
        autoResetTimer = nil
        // print("[SubtitleItem] 滑动状态已重置")
    }

    // 添加启动自动回计时器的方法
    private func startAutoResetTimer() {
        autoResetTimer?.invalidate() // 清除已存在的计时器
        autoResetTimer = Timer.scheduledTimer(withTimeInterval: autoResetDelay, repeats: false) { _ in
            resetSwipeState()
        }
    }

    // 添加获取单词颜色辅助法
    private func getWordColor(word: String) -> Color {
        if wordSelectionManager.selectedWord?.word == word {
            return ThemeColors.primary
        } else if isHighlighted {
            return ThemeColors.primary.opacity(0.9)
        } else {
            return ThemeColors.secondaryText(viewModel.currentTheme)
        }
    }

    // 修改英文单词验证函数
    private func isValidEnglishWord(_ word: String) -> Bool {
        // 1. 移除所有标点符号，但保留撇号，转换为小写
        let cleanWord = word.trimmingCharacters(in: CharacterSet.punctuationCharacters.subtracting(CharacterSet(charactersIn: "'"))).lowercased()

        // 2. 检查是否包含中文字符
        if containsChinese(cleanWord) {
            return false
        }

        // 3. 正则表达式匹配英文单词（包括带撇号的）
        let pattern = "^[a-z]+('[a-z]+)?$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(cleanWord.startIndex..., in: cleanWord)

        return cleanWord.count > 0 &&
               regex?.firstMatch(in: cleanWord, range: range) != nil &&
               cleanWord.rangeOfCharacter(from: .decimalDigits) == nil
    }

    // 检查否包含中文字符
    private func containsChinese(_ text: String) -> Bool {
        let pattern = "[\u{4e00}-\u{9fff}]"
        return text.range(of: pattern, options: .regularExpression) != nil
    }

    // 添加 Equatable 扩展
    private func areEqual(_ lhs: (String, CGRect)?, _ rhs: (String, CGRect)?) -> Bool {
        switch (lhs, rhs) {
        case (.none, .none):
            return true
        case let (lhs?, rhs?):
            return lhs.0 == rhs.0 && lhs.1 == rhs.1
        default:
            return false
        }
    }

    // 修改 isDisabledForABLoop 计算属性
    private var isDisabledForABLoop: Bool {
        guard viewModel.playMode == .abLoop else { return false }

        if let startTime = viewModel.abLoopStartTime {
            if let endTime = viewModel.abLoopEndTime {
                // 如果 AB 点都已设置，则 A 点之前和 B 点之后的字幕禁用
                return subtitle.endTime < startTime || subtitle.startTime > endTime
            } else {
                // 如果只设置了 A 点，则 A 点之前的字幕禁用
                return subtitle.endTime < startTime
            }
        }
        return false
    }

}

// 替换原有的 String 扩展
struct IdentifiableWord: Identifiable, Hashable {
    let word: String
    var id: String { word }
}

struct WordFramePreferenceKey: PreferenceKey {
    static var defaultValue: [IdentifiableWord: CGRect] = [:]

    static func reduce(value: inout [IdentifiableWord: CGRect], nextValue: () -> [IdentifiableWord: CGRect]) {
        value.merge(nextValue()) { $1 }
    }
}

// 添加常量定义
private struct GestureConstants {
    static let minimumGestureDistance: CGFloat = 30.0    // 提高最小手势距离
    static let horizontalThreshold: CGFloat = 50.0       // 提高水平判定阈值
    static let verticalThreshold: CGFloat = 20.0         // 保持垂直判定阈值
    static let swipeThreshold: CGFloat = 80.0           // 保持滑动触发阈值
    static let velocityThreshold: CGFloat = 500.0       // 提高速度阈值
    static let angleThreshold: CGFloat = 0.2            // 降低角度阈值（约11度）
}
