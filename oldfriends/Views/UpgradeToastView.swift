import SwiftUI

struct UpgradeToastView: View {
    let theme: AppTheme
    @Binding var isPresented: Bool
    let onTap: () -> Void
    let onClose: () -> Void
    
    var body: some View {
        VStack {
            Spacer()
            HStack(spacing: 4) {
                Button(action: {
                    onTap()
                    withAnimation(.easeOut) {
                        isPresented = false
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "crown.fill")
                            .foregroundColor(ThemeColors.primary)
                            .font(.system(size: 14))
                        
                        Text("听更多剧集，解锁更多精彩")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(ThemeColors.primaryText(theme))
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(ThemeColors.secondaryText(theme))
                            .font(.system(size: 12))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                ThemeColors.primary.opacity(0.15),
                                ThemeColors.primary.opacity(0.1)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .cornerRadius(16)
                }
                .buttonStyle(ScaleButtonStyle())
                
                // 关闭按钮
                Button(action: {
                    withAnimation(.easeOut) {
                        isPresented = false
                    }
                    onClose()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(ThemeColors.secondaryText(theme).opacity(0.6))
                        .frame(width: 24, height: 24)
                        .contentShape(Rectangle())
                }
                .buttonStyle(ScaleButtonStyle())
            }
            .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
            .padding(.bottom, 70)
        }
        .transition(.move(edge: .bottom).combined(with: .opacity))
        .onAppear {
            // 15秒后自动消失
            DispatchQueue.main.asyncAfter(deadline: .now() + 15) {
                withAnimation(.easeOut) {
                    isPresented = false
                }
            }
        }
    }
}

// 添加按钮缩放动画效果
private struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
} 
