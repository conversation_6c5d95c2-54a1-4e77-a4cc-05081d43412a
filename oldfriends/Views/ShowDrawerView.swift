import SwiftUI
import os
import StoreHelper

// MARK: - ShowDrawerView
struct ShowDrawerView: View {
    let theme: AppTheme
    @EnvironmentObject var playerViewModel: PlayerViewModel

    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @Environment(\.dismiss) var dismiss
    @State private var selectedSeason: Int = 1
    @State private var showNetworkAlert = false

    var body: some View {
        NavigationView {
            DrawerContentView(
                theme: theme,
                selectedSeason: $selectedSeason,
                dismiss: dismiss
            )
        }
        .onAppear {
            // 初始化选中当前播放的季
            selectedSeason = playerViewModel.currentSeasonNumber
            // 添加页面访问埋点
            AnalyticsManager.shared.logShowListScreenView()
        }
    }
}

// MARK: - DrawerContentView
private struct DrawerContentView: View {
    let theme: AppTheme
    @EnvironmentObject var playerViewModel: PlayerViewModel
    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @StateObject private var membershipManager = MembershipManager.shared
    @Binding var selectedSeason: Int
    let dismiss: DismissAction

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            DrawerHeaderView(theme: theme, dismiss: dismiss)

            ScrollView {
                VStack(spacing: 5) {
                    // 会员状态 - 直接使用 membershipManager.isLifetimeMember
                    if !membershipManager.isLifetimeMember {
                        // 添加顶部间距
                        Spacer()
                            .frame(height: 4)

                        // 合并会员状态栏和会员引导为一个卡片
                        CombinedMembershipView(
                            theme: theme,
                            dismiss: dismiss
                        )
                    }

                    // 季数选择器
                    SeasonPickerView(
                        theme: theme,
                        selectedSeason: $selectedSeason,
                        dismiss: dismiss
                    )

                    // 剧集列表
                    EpisodeListView(
                        theme: theme,
                        selectedSeason: selectedSeason,
                        dismiss: dismiss
                    )
                }
            }
        }
        .navigationBarHidden(true)
        .background(ThemeColors.background(theme))
    }
}

// MARK: - DrawerHeaderView
private struct DrawerHeaderView: View {
    let theme: AppTheme
    let dismiss: DismissAction

    var body: some View {
        VStack(spacing: 0) {
            // 顶部拖拽指示器
            RoundedRectangle(cornerRadius: 2.5)
                .fill(ThemeColors.secondaryText(theme).opacity(0.3))
                .frame(width: 36, height: 5)
                .padding(.top, 8)

            HStack(spacing: 12) {
                // 课程图标
                ZStack {
                    Circle()
                        .fill(ThemeColors.primary.opacity(0.1))
                        .frame(width: 32, height: 32)

                    Image(systemName: "graduationcap.fill")
                        .foregroundColor(ThemeColors.primary)
                        .font(.system(size: 16, weight: .medium))
                }

                Text("课程选择")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(ThemeColors.primaryText(theme))

                Spacer()

                Button(action: {
                    // 添加关闭按钮点击埋点
                    AnalyticsManager.shared.logShowListCloseClick()
                    dismiss()
                }) {
                    ZStack {
                        Circle()
                            .fill(ThemeColors.secondaryText(theme).opacity(0.1))
                            .frame(width: 32, height: 32)

                        Image(systemName: "xmark")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(ThemeColors.secondaryText(theme))
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)

            // 渐变分割线
            LinearGradient(
                gradient: Gradient(colors: [
                    ThemeColors.border(theme).opacity(0.3),
                    ThemeColors.border(theme).opacity(0.1),
                    Color.clear
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
            .frame(height: 1)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    ThemeColors.surface(theme),
                    ThemeColors.background(theme).opacity(0.8)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .shadow(color: theme == .dark ? Color.black.opacity(0.2) : Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
    }
}

// MARK: - SeasonPickerView
private struct SeasonPickerView: View {
    let theme: AppTheme
    @Binding var selectedSeason: Int
    @EnvironmentObject var playerViewModel: PlayerViewModel
    let dismiss: DismissAction
    @StateObject private var membershipManager = MembershipManager.shared

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 季数选择标题 - 简化设计
            HStack {
                Text("季数")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(ThemeColors.primaryText(theme))

                Spacer()
            }
            .padding(.horizontal, 20)

            ScrollView(.horizontal, showsIndicators: false) {
                LazyVGrid(columns: [
                    GridItem(.fixed(80)),
                    GridItem(.fixed(80)),
                    GridItem(.fixed(80)),
                    GridItem(.fixed(80))
                ], spacing: 12) {
                    // 基于实际数据动态生成季度按钮
                    if let show = ShowManager.shared.loadShow() {
                        ForEach(show.seasons, id: \.number) { season in
                            let seasonNumber = season.number
                            let isUnlocked = seasonNumber == 1 || membershipManager.isLifetimeMember
                            let isCurrentlyPlaying = seasonNumber == playerViewModel.currentSeasonNumber
                            let isSelected = seasonNumber == selectedSeason

                            Button(action: {
                                // 添加季数按钮点击埋点
                                AnalyticsManager.shared.logSeasonButtonClick()
                                if isUnlocked {
                                    selectedSeason = seasonNumber
                                } else {
                                // 标记是从剧集选择页面触发的购买
                                membershipManager.isFromShowDrawer = true

                                // 先关闭当前视图，然后通过通知中心触发购买页面显示
                                dismiss()

                                // 延迟一帧再发送通知，确保购买页面已完全关闭
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    NotificationCenter.default.post(
                                        name: NSNotification.Name("ShowPurchaseView"),
                                        object: nil
                                    )
                                }
                            }
                        }) {
                            // 现代化的季数标签设计
                            HStack(spacing: 6) {
                                // 当前播放季显示播放图标
                                if isCurrentlyPlaying {
                                    Image(systemName: "play.fill")
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(isSelected ? .white : ThemeColors.primary)
                                }

                                Text("S\(seasonNumber)")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(isSelected ? .white : ThemeColors.primaryText(theme))

                                // 锁图标
                                if !isUnlocked {
                                    Image(systemName: "lock.fill")
                                        .font(.system(size: 11, weight: .medium))
                                        .foregroundColor(isSelected ? .white.opacity(0.8) : ThemeColors.secondaryText(theme))
                                }
                            }
                            .frame(height: 36) // 现代化的更紧凑高度
                            .padding(.horizontal, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 8) // 更小的圆角
                                    .fill(
                                        isSelected ?
                                        ThemeColors.primary : // 简化为纯色背景
                                        ThemeColors.surface(theme)
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(
                                                isSelected ?
                                                Color.clear :
                                                ThemeColors.border(theme).opacity(0.3),
                                                lineWidth: 1
                                            )
                                    )
                            )
                            .shadow(
                                color: isSelected ? ThemeColors.primary.opacity(0.2) : Color.black.opacity(theme == .dark ? 0.1 : 0.05),
                                radius: isSelected ? 4 : 2,
                                x: 0,
                                y: isSelected ? 2 : 1
                            )
                            .opacity(isUnlocked ? 1 : 0.5)
                            .scaleEffect(isSelected ? 1.02 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - EpisodeListView
private struct EpisodeListView: View {
    let theme: AppTheme
    @EnvironmentObject var playerViewModel: PlayerViewModel
    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @StateObject private var membershipManager = MembershipManager.shared
    let selectedSeason: Int
    let dismiss: DismissAction

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let show = ShowManager.shared.loadShow() {
                    if let season = show.seasons.first(where: { $0.number == selectedSeason }) {
                        ForEach(Array(season.episodes.enumerated()), id: \.element.number) { index, episode in
                            EpisodeItemView(
                                theme: theme,
                                episode: episode,
                                selectedSeason: selectedSeason,
                                dismiss: dismiss
                            )
                            .opacity(index < 5 || membershipManager.isLifetimeMember ? 1 : 0.6)
                        }
                    } else {
                        Text("未找到第\(selectedSeason)季数据")
                            .foregroundColor(ThemeColors.secondaryText(theme))
                    }
                } else {
                    Text("加载剧集数据失败")
                        .foregroundColor(.red)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 6)
            .padding(.bottom, 16)
        }
    }
}

// MARK: - EpisodeItemView
private struct EpisodeItemView: View {
    let theme: AppTheme
    @EnvironmentObject var playerViewModel: PlayerViewModel
    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @StateObject private var membershipManager = MembershipManager.shared
    let episode: Episode
    let selectedSeason: Int
    let dismiss: DismissAction

    // 计算属性：根据季数和剧集编号确定是否解锁
    private var isUnlocked: Bool {
        // 第一季的前两集免费
        if selectedSeason == 1 && episode.number <= 2 {
            return true
        }
        // 会员可以看所有剧集
        return membershipManager.isLifetimeMember
    }

    // 判断是否是第一季第一集
    private var isFirstEpisode: Bool {
        return selectedSeason == 1 && episode.number == 1
    }

    // 判断是否是当前播放的剧集
    private var isCurrentlyPlaying: Bool {
        return selectedSeason == playerViewModel.currentSeasonNumber &&
               episode.number == playerViewModel.currentEpisodeNumber
    }

    var body: some View {
        Button(action: {
            if isUnlocked {
                print("[ShowDrawerView] 选择剧集: 第\(selectedSeason)季第\(episode.number)集")
                // 添加剧集点击埋点
                AnalyticsManager.shared.logEpisodeItemClick()
                Task {
                    // 先检查是否可以播放该剧集
                    if membershipManager.canPlayEpisode(seasonNumber: selectedSeason, episodeNumber: episode.number) {
                        print("[ShowDrawerView] 开始加载剧集: 第\(episode.number)集")
                        // 可以播放，加载剧集
                        playerViewModel.loadEpisode(
                            audioName: episode.audioName,
                            subtitleName: episode.subtitleName,
                            seasonNumber: selectedSeason,
                            episodeNumber: episode.number,
                            episodeTitle: episode.title
                        )
                        // 关闭抽屉
                        dismiss()
                    }
                }
            } else {
                // 添加剧集点击埋点
                AnalyticsManager.shared.logEpisodeItemClick()

                // 标记是从剧集选择页面触发的购买
                membershipManager.isFromShowDrawer = true

                // 先关闭当前视图，然后通过通知中心触发购买页面显示
                dismiss()

                // 延迟一帧再发送通知，确保购买页面已完全关闭
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ShowPurchaseView"),
                        object: nil
                    )
                }
            }
        }) {
            HStack(spacing: 16) {
                // 剧集编号圆圈
                ZStack {
                    Circle()
                        .fill(
                            isCurrentlyPlaying ?
                            LinearGradient(
                                gradient: Gradient(colors: [ThemeColors.primary, ThemeColors.primary.opacity(0.8)]),
                                startPoint: .top,
                                endPoint: .bottom
                            ) :
                            LinearGradient(
                                gradient: Gradient(colors: [ThemeColors.primary.opacity(0.1), ThemeColors.primary.opacity(0.05)]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(width: 44, height: 44)

                    if isCurrentlyPlaying {
                        // 使用音波图标表示正在播放，避免与右侧播放图标重复
                        Image(systemName: "waveform")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    } else {
                        Text("S\(selectedSeason)E\(episode.number)")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(isUnlocked ? ThemeColors.primary : ThemeColors.secondaryText(theme))
                    }
                }

                // 剧集信息
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(episode.title)
                            .font(.system(size: 15, weight: .medium)) // 保持调整后的字体大小和字重，与页面设计更和谐
                            .foregroundColor(ThemeColors.primaryText(theme))
                            .lineLimit(1)

                        Spacer()

                        // 状态图标 - 统一设计
                        if !isUnlocked {
                            ZStack {
                                Circle()
                                    .fill(ThemeColors.secondaryText(theme).opacity(0.1))
                                    .frame(width: 24, height: 24)

                                Image(systemName: "lock.fill")
                                    .font(.system(size: 11, weight: .medium)) // 统一图标大小和字重
                                    .foregroundColor(ThemeColors.secondaryText(theme))
                            }
                        } else if isCurrentlyPlaying {
                            ZStack {
                                Circle()
                                    .fill(ThemeColors.primary.opacity(0.1))
                                    .frame(width: 24, height: 24)

                                Image(systemName: "play.fill")
                                    .font(.system(size: 11, weight: .medium)) // 统一图标大小和字重
                                    .foregroundColor(ThemeColors.primary) // 使用主题色而非绿色
                            }
                        } else {
                            // 已解锁剧集显示下载图标
                            if !isFirstEpisode {
                                DownloadStatusView(
                                    theme: theme,
                                    audioName: episode.audioName,
                                    subtitleName: episode.subtitleName
                                )
                            } else {
                                ZStack {
                                    Circle()
                                        .fill(ThemeColors.primary.opacity(0.1))
                                        .frame(width: 24, height: 24)

                                    Image(systemName: "checkmark")
                                        .font(.system(size: 11, weight: .medium)) // 统一图标大小和字重
                                        .foregroundColor(ThemeColors.primary) // 使用主题色保持一致
                                }
                            }
                        }
                    }
                }

                // 右侧箭头
                if isUnlocked {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(ThemeColors.secondaryText(theme).opacity(0.6))
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isCurrentlyPlaying ?
                        LinearGradient(
                            gradient: Gradient(colors: [ThemeColors.primary.opacity(0.08), ThemeColors.primary.opacity(0.04)]),
                            startPoint: .top,
                            endPoint: .bottom
                        ) :
                        LinearGradient(
                            gradient: Gradient(colors: [ThemeColors.surface(theme), ThemeColors.background(theme).opacity(0.5)]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isCurrentlyPlaying ? ThemeColors.primary.opacity(0.3) : ThemeColors.border(theme).opacity(0.3),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: isCurrentlyPlaying ? ThemeColors.primary.opacity(0.15) : Color.black.opacity(theme == .dark ? 0.2 : 0.08),
                radius: isCurrentlyPlaying ? 8 : 4,
                x: 0,
                y: isCurrentlyPlaying ? 4 : 2
            )
            .opacity(isUnlocked ? 1 : 0.6)
            .scaleEffect(isCurrentlyPlaying ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isCurrentlyPlaying)
        }
    }
}

// MARK: - DownloadStatusView
private struct DownloadStatusView: View {
    let theme: AppTheme
    @StateObject private var downloadViewModel = DownloadViewModel.shared
    @EnvironmentObject var playerViewModel: PlayerViewModel

    var audioName: String
    var subtitleName: String

    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier!, category: "DownloadStatusView")

    var body: some View {
        HStack(spacing: 8) {
            if downloadViewModel.isDownloading && downloadViewModel.currentDownloadingFile.contains(audioName) {
                // 下载中状态
                ProgressView(value: downloadViewModel.downloadProgress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .frame(width: 60)

                Text("\(Int(downloadViewModel.downloadProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else if isDownloaded {
                // 已下载状态 - 使用主题色保持一致
                ZStack {
                    Circle()
                        .fill(ThemeColors.primary.opacity(0.1))
                        .frame(width: 24, height: 24)

                    Image(systemName: "checkmark")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(ThemeColors.primary)
                }
            } else {
                // 未下载状态
                Button(action: {
                    logger.info("点击下载按钮: \(audioName)")

                    // 解析audioName获取季数和集数
                    if audioName.hasPrefix("S") && audioName.contains("E") {
                        // 处理 S01E02 格式
                        let seasonPattern = "S(\\d+)"
                        let episodePattern = "E(\\d+)"

                        if let seasonRange = audioName.range(of: seasonPattern, options: .regularExpression),
                           let episodeRange = audioName.range(of: episodePattern, options: .regularExpression) {

                            let seasonStr = audioName[seasonRange].dropFirst()
                            let episodeStr = audioName[episodeRange].dropFirst()

                            if let seasonNumber = Int(seasonStr),
                               let episodeNumber = Int(episodeStr) {

                                logger.info("解析成功 - 季数: \(seasonNumber), 集数: \(episodeNumber)")

                                // 开始下载
                                downloadViewModel.startDownload(
                                    audioName: audioName,
                                    subtitleName: subtitleName,
                                    seasonNumber: seasonNumber,
                                    episodeNumber: episodeNumber,
                                    episodeTitle: "Episode \(episodeNumber)",
                                    completion: {
                                        logger.info("下载完成回调: \(audioName)")
                                    }
                                )
                                return
                            }
                        }
                    }

                    // 尝试旧的解析方式（兼容性保留）
                    let components = audioName.components(separatedBy: "_")
                    if components.count >= 3,
                       let seasonStr = components[1].split(separator: "s").last,
                       let episodeStr = components[2].split(separator: "e").last,
                       let seasonNumber = Int(seasonStr),
                       let episodeNumber = Int(episodeStr) {

                        logger.info("解析成功 - 季数: \(seasonNumber), 集数: \(episodeNumber)")

                        // 开始下载
                        downloadViewModel.startDownload(
                            audioName: audioName,
                            subtitleName: subtitleName,
                            seasonNumber: seasonNumber,
                            episodeNumber: episodeNumber,
                            episodeTitle: "Episode \(episodeNumber)",
                            completion: {
                                logger.info("下载完成回调: \(audioName)")
                            }
                        )
                    } else {
                        logger.error("无法解析音频名称: \(audioName)")
                    }
                }) {
                    Image(systemName: "arrow.down.circle")
                        .foregroundColor(ThemeColors.primary)
                }
            }
        }
        .onAppear {
            logger.info("DownloadStatusView出现 - audioName: \(audioName)")
            logger.info("下载状态 - isDownloading: \(downloadViewModel.isDownloading)")
            logger.info("当前下载文件: \(downloadViewModel.currentDownloadingFile)")
            logger.info("下载进度: \(downloadViewModel.downloadProgress)")
            logger.info("是否已下载: \(isDownloaded)")

            // 检查OSS配置
            OSSConfig.checkConfiguration()
        }
    }

    // 判断是否已下载
    private var isDownloaded: Bool {
        // 使用文件名作为缓存key
        let cacheKey = "\(audioName)_\(subtitleName)"

        // 如果缓存中存在且未过期，直接返回缓存结果
        if let cachedResult = DownloadStatusCache.shared.get(key: cacheKey) {
            return cachedResult
        }

        // 进行实际验证
        let result = downloadViewModel.areResourceFilesComplete(audioName: audioName, subtitleName: subtitleName)

        // 将结果存入缓存
        DownloadStatusCache.shared.set(key: cacheKey, value: result)

        // 只在首次验证或状态变化时输出日志
        logger.info("检查文件是否已下载 \(audioName): \(result ? "是" : "否")")
        return result
    }
}

// MARK: - DownloadStatusCache
class DownloadStatusCache {
    static let shared = DownloadStatusCache()
    private var cache: [String: (result: Bool, timestamp: Date)] = [:]
    private let cacheTimeout: TimeInterval = 5 // 缓存有效期5秒

    private init() {}

    func get(key: String) -> Bool? {
        guard let cached = cache[key] else { return nil }

        // 检查缓存是否过期
        if Date().timeIntervalSince(cached.timestamp) > cacheTimeout {
            cache.removeValue(forKey: key)
            return nil
        }

        return cached.result
    }

    func set(key: String, value: Bool) {
        cache[key] = (value, Date())
    }

    func clear() {
        cache.removeAll()
    }
}

// MARK: - CombinedMembershipView
private struct CombinedMembershipView: View {
    let theme: AppTheme
    let dismiss: DismissAction
    @StateObject private var membershipManager = MembershipManager.shared
    // @EnvironmentObject private var storeHelper: StoreHelper
    let productId = Constants.Store.premiumProductId

    var body: some View {
        VStack(spacing: 16) {
            // 会员卡片 - 简化设计
            VStack(spacing: 16) {
                // 更具煽动性的文案
                VStack(spacing: 8) {
                    Text("和老友们一起，从英语小白到流利达人！")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(ThemeColors.primaryText(theme))
                        .multilineTextAlignment(.center)
                        .lineSpacing(2)

                    Text("解锁240集完整剧情，沉浸式学习地道美式英语")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(ThemeColors.secondaryText(theme))
                        .multilineTextAlignment(.center)
                        .lineSpacing(1)
                }

                // 立即解锁按钮
                Button(action: {
                    // 添加立即解锁按钮点击埋点
                    AnalyticsManager.shared.logShowListUnlockClick()
                    dismiss()
                    // 延迟一帧再显示购买页面，并标记是从剧集选择页面触发的购买
                    DispatchQueue.main.async {
                        membershipManager.isFromShowDrawer = true
                        membershipManager.showPurchaseAlert = true
                    }
                }) {
                    Text("立即解锁全部内容")
                        .font(.system(size: 16, weight: .semibold))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(ThemeColors.primary)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
            .padding(16)
            .background(ThemeColors.surface(theme))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(ThemeColors.border(theme), lineWidth: 0.5)
            )
            .shadow(color: theme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
            .padding(.horizontal, 20)
        }
    }
}